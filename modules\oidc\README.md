# OIDC Module

This module manages the OpenID Connect (OIDC) identity provider for EKS clusters, enabling IAM Roles for Service Accounts (IRSA) functionality.

## 🎯 Purpose

The OIDC module provides:
- **OIDC Provider Creation**: Sets up AWS IAM OIDC identity provider for EKS
- **Certificate Management**: Handles TLS certificate verification for trust
- **IRSA Support**: Enables IAM Roles for Service Accounts authentication
- **Integration Outputs**: Provides necessary values for other modules

## 📁 Module Structure

```
modules/oidc/
├── main.tf          # OIDC provider and certificate data sources
├── variables.tf     # Input variables and validation
├── outputs.tf       # Output values for integration
└── README.md        # This documentation
```

## 🚀 Features

### OIDC Provider Management
- **Automated Certificate Retrieval**: Fetches and validates EKS cluster certificates
- **Trust Establishment**: Creates secure trust relationship between AWS IAM and EKS
- **Thumbprint Verification**: Ensures only legitimate cluster tokens are accepted

### IRSA Enablement
- **Service Account Authentication**: Allows Kubernetes service accounts to assume IAM roles
- **Token-Based Access**: Uses JWT tokens for secure AWS API access
- **No Long-Term Credentials**: Eliminates need for static AWS credentials in pods

### Integration Support
- **Flexible Outputs**: Provides various formats for different integration needs
- **Trust Policy Helpers**: Includes pre-formatted conditions for IAM policies
- **Conditional Creation**: Only creates resources when IRSA is enabled

## 📋 Usage

### Basic Usage
```hcl
module "oidc" {
  source = "./modules/oidc"

  # Basic Configuration
  project_name = "my-project"
  environment  = "dev"

  # OIDC Configuration
  enable_irsa              = true
  cluster_oidc_issuer_url  = module.eks.cluster_oidc_issuer_url

  common_tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### Disabled IRSA (Pod Identity Only)
```hcl
module "oidc" {
  source = "./modules/oidc"

  # Basic Configuration
  project_name = "my-project"
  environment  = "prod"

  # Disable IRSA (use Pod Identity instead)
  enable_irsa              = false
  cluster_oidc_issuer_url  = ""  # Not needed when disabled

  common_tags = var.common_tags
}
```

### Integration with Identity Module
```hcl
module "identity" {
  source = "./modules/identity"

  # ... other configuration ...

  # OIDC Integration
  enable_pod_identity = false  # Use IRSA instead
  oidc_provider_arn   = module.oidc.provider_arn
  oidc_issuer_url     = module.oidc.issuer_url
}
```

## 🔧 Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `project_name` | `string` | - | Name of the project |
| `environment` | `string` | - | Environment name (dev, staging, prod) |
| `common_tags` | `map(string)` | `{}` | Common tags to apply to all resources |
| `enable_irsa` | `bool` | `true` | Enable IRSA by creating OIDC provider |
| `cluster_oidc_issuer_url` | `string` | - | EKS cluster OIDC issuer URL |

## 📤 Outputs

### Core Outputs
- `provider_arn` - ARN of the OIDC Provider for IRSA
- `provider_url` - URL of the OIDC Provider
- `issuer_url` - OIDC issuer URL without https:// prefix
- `thumbprint` - Thumbprint of the OIDC provider certificate

### Integration Outputs
- `enabled` - Whether IRSA/OIDC provider is enabled
- `trust_policy_condition` - Common condition block for IAM trust policies
- `oidc_provider_details` - Complete provider information

## 🔒 Security Considerations

### Certificate Validation
- Automatically retrieves and validates EKS cluster certificates
- Uses SHA1 fingerprint for secure thumbprint verification
- Ensures only tokens from the legitimate cluster are accepted

### Trust Boundaries
- OIDC provider only trusts AWS STS as a client
- Trust policies require specific audience (sts.amazonaws.com)
- Service account and namespace conditions can be added for fine-grained access

### Token Security
- JWT tokens are short-lived and automatically rotated
- No long-term credentials stored in pods
- Tokens are scoped to specific service accounts and namespaces

## 🔗 Integration

This module is designed to work with:
- **EKS Module**: Consumes cluster OIDC issuer URL
- **Identity Module**: Provides OIDC provider details for IAM role trust policies
- **Helm Module**: Enables service accounts to assume IAM roles for AWS services

## 📝 Notes

### Pod Identity vs IRSA
- **Pod Identity**: AWS's newer approach, simpler and more secure
- **IRSA**: Legacy approach, still widely used and supported
- This module supports IRSA; disable when using Pod Identity exclusively

### Dependencies
- Requires EKS cluster to be created first (for OIDC issuer URL)
- Must be created before any IAM roles that trust the OIDC provider
- Certificate data is fetched dynamically during Terraform apply

### Troubleshooting
- Ensure EKS cluster OIDC issuer is enabled
- Verify the issuer URL is accessible and returns valid certificates
- Check that the thumbprint matches the actual cluster certificate
