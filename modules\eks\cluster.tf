# =============================================================================
# EKS CLUSTER CONFIGURATION
# =============================================================================
# This file contains the core EKS cluster configuration including:
# - CloudWatch log group for cluster logging
# - EKS cluster with control plane configuration
# - OIDC provider for IAM Roles for Service Accounts (IRSA)

# -----------------------------------------------------------------------------
# CLOUDWATCH LOG GROUP
# -----------------------------------------------------------------------------
# Creates a dedicated CloudWatch log group for EKS cluster control plane logs
# This enables monitoring and troubleshooting of the Kubernetes API server,
# audit logs, authenticator, controller manager, and scheduler
resource "aws_cloudwatch_log_group" "eks" {
  name              = "/aws/eks/${var.cluster_name}/cluster" # Standard AWS naming convention
  retention_in_days = var.cluster_log_retention_days         # Configurable retention period

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-logs"
    }
  )
}

# -----------------------------------------------------------------------------
# EKS CLUSTER
# -----------------------------------------------------------------------------
# Creates the managed EKS cluster with control plane components
# The control plane includes the Kubernetes API server, etcd, and other components
resource "aws_eks_cluster" "main" {
  name     = var.cluster_name             # Unique cluster identifier
  version  = var.cluster_version          # Kubernetes version (e.g., "1.28")
  role_arn = var.cluster_service_role_arn # IAM role for EKS service from Identity module

  # VPC and networking configuration
  vpc_config {
    subnet_ids              = var.subnet_ids                   # Subnets for control plane ENIs
    endpoint_private_access = var.endpoint_private_access      # Enable private API endpoint
    endpoint_public_access  = var.endpoint_public_access       # Enable public API endpoint
    public_access_cidrs     = var.endpoint_public_access_cidrs # CIDR blocks for public access
  }

  # Modern access configuration using EKS Access Entries API
  # This replaces the traditional aws-auth ConfigMap approach
  access_config {
    authentication_mode                         = var.authentication_mode                         # API, API_AND_CONFIG_MAP, or CONFIG_MAP
    bootstrap_cluster_creator_admin_permissions = var.bootstrap_cluster_creator_admin_permissions # Auto-grant admin to creator
  }

  # Control plane logging configuration
  # Enables specific log types: api, audit, authenticator, controllerManager, scheduler
  enabled_cluster_log_types = var.enable_cluster_log_types

  # Ensure dependencies are created first
  depends_on = [
    aws_cloudwatch_log_group.eks, # Log group for cluster logs
  ]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-cluster"
    }
  )
}

# -----------------------------------------------------------------------------
# OIDC PROVIDER FOR IRSA (IAM ROLES FOR SERVICE ACCOUNTS)
# -----------------------------------------------------------------------------
# Note: OIDC provider is now managed by the separate OIDC module
# This provides better separation of concerns and allows for reuse across multiple EKS clusters
