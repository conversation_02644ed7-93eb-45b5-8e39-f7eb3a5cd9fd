# ECR Registry Module

This module manages Amazon Elastic Container Registry (ECR) repositories for storing container images used in GitOps workflows and application deployments.

## 🎯 Purpose

The ECR Registry module provides:
- **Container Registry Management**: Creates and manages ECR repositories
- **Security Features**: Image scanning, encryption, and access controls
- **Cost Optimization**: Lifecycle policies for automatic image cleanup
- **GitOps Integration**: Outputs formatted for CI/CD and GitOps workflows

## 📁 Module Structure

```
modules/ecr_registry/
├── main.tf          # ECR repositories, policies, and lifecycle rules
├── variables.tf     # Input variables and validation
├── outputs.tf       # Output values for integration
└── README.md        # This documentation
```

## 🚀 Features

### Repository Management
- **Multiple Repositories**: Creates multiple ECR repositories from a list
- **Consistent Naming**: Applies project and environment prefixes
- **Tag Mutability**: Configurable image tag mutability (MUTABLE/IMMUTABLE)

### Security & Compliance
- **Image Scanning**: Automatic vulnerability scanning on image push
- **Encryption**: AES256 or KMS encryption for images at rest
- **Access Control**: Cross-account access policies when needed

### Cost Optimization
- **Lifecycle Policies**: Automatic cleanup of old and untagged images
- **Retention Rules**: Configurable image retention policies
- **Tag-Based Cleanup**: Different rules for tagged and untagged images

### Integration Support
- **Docker Commands**: Pre-formatted Docker commands for each repository
- **GitOps Ready**: Outputs formatted for GitOps workflows
- **Helm Integration**: Image values ready for Helm charts

## 📋 Usage

### Basic Usage
```hcl
module "ecr_registry" {
  source = "./modules/ecr_registry"

  # Basic Configuration
  project_name = "my-project"
  environment  = "dev"

  # Repository Configuration
  repository_names = [
    "frontend",
    "backend",
    "api",
    "worker"
  ]

  # Security Configuration
  enable_image_scanning = true
  encryption_type      = "AES256"

  # Lifecycle Configuration
  enable_lifecycle_policy = true
  max_image_count        = 10
  untagged_image_days    = 7

  common_tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### Production Configuration with KMS
```hcl
module "ecr_registry" {
  source = "./modules/ecr_registry"

  # Basic Configuration
  project_name = "my-project"
  environment  = "prod"

  # Repository Configuration
  repository_names = [
    "app-frontend",
    "app-backend",
    "app-api"
  ]
  image_tag_mutability = "IMMUTABLE"

  # Security Configuration
  enable_image_scanning = true
  encryption_type      = "KMS"
  kms_key_id          = aws_kms_key.ecr.id

  # Lifecycle Configuration
  enable_lifecycle_policy = true
  max_image_count        = 20
  untagged_image_days    = 3
  lifecycle_tag_prefixes = ["v", "release"]

  # Cross-account access
  enable_cross_account_access = true
  cross_account_arns = [
    "arn:aws:iam::************:root",
    "arn:aws:iam::************:root"
  ]

  common_tags = var.common_tags
}
```

### GitOps Integration
```hcl
# Use ECR repositories in GitOps workflows
locals {
  argocd_values = {
    image = {
      repository = module.ecr_registry.repository_urls["argocd"]
      tag        = "v2.8.0"
    }
  }
}
```

## 🔧 Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `project_name` | `string` | - | Name of the project |
| `environment` | `string` | - | Environment name (dev, staging, prod) |
| `common_tags` | `map(string)` | `{}` | Common tags to apply to all resources |
| `repository_names` | `list(string)` | `["app-frontend", "app-backend", ...]` | List of repository names to create |
| `image_tag_mutability` | `string` | `"MUTABLE"` | Tag mutability (MUTABLE/IMMUTABLE) |
| `enable_image_scanning` | `bool` | `true` | Enable vulnerability scanning |
| `encryption_type` | `string` | `"AES256"` | Encryption type (AES256/KMS) |
| `kms_key_id` | `string` | `null` | KMS key ID for encryption |
| `enable_lifecycle_policy` | `bool` | `true` | Enable lifecycle policy |
| `max_image_count` | `number` | `10` | Maximum images to keep |
| `untagged_image_days` | `number` | `7` | Days to keep untagged images |
| `lifecycle_tag_prefixes` | `list(string)` | `["v", "release", ...]` | Tag prefixes for lifecycle |
| `enable_cross_account_access` | `bool` | `false` | Enable cross-account access |
| `cross_account_arns` | `list(string)` | `[]` | Cross-account ARNs |

## 📤 Outputs

### Repository Information
- `repository_urls` - Map of repository names to URLs
- `repository_arns` - Map of repository names to ARNs
- `repository_names` - Map of original to full repository names
- `repositories` - Complete repository information

### Integration Outputs
- `docker_login_command` - AWS CLI command for Docker authentication
- `docker_commands` - Docker commands for each repository
- `gitops_repositories` - Repository info for GitOps workflows
- `helm_image_values` - Image values for Helm charts

### Summary
- `summary` - Complete configuration summary

## 🔒 Security Considerations

### Image Security
- Enable image scanning to detect vulnerabilities
- Use immutable tags in production environments
- Implement proper lifecycle policies to remove vulnerable images

### Access Control
- Use cross-account policies only when necessary
- Implement least-privilege access principles
- Consider using IAM roles instead of long-term credentials

### Encryption
- Use KMS encryption for sensitive workloads
- Manage KMS keys separately with proper rotation policies
- Consider compliance requirements for encryption at rest

## 💰 Cost Optimization

### Lifecycle Policies
- Automatically clean up old images to reduce storage costs
- Configure appropriate retention periods for your use case
- Use different rules for production vs development repositories

### Image Management
- Use multi-stage builds to reduce image sizes
- Implement proper tagging strategies
- Monitor repository sizes and usage patterns

## 🔗 Integration

This module is designed to work with:
- **GitOps Workflows**: Provides repository URLs for CI/CD pipelines
- **Helm Charts**: Outputs image values for Kubernetes deployments
- **EKS Module**: Container images for applications running on EKS
- **CI/CD Systems**: Docker commands and authentication for build pipelines

## 📝 Notes

### Repository Naming
- Repositories are named with project and environment prefixes
- Original names are preserved in outputs for easy reference
- Consider naming conventions that align with your deployment strategy

### Lifecycle Policies
- Policies apply to all repositories created by this module
- Customize tag prefixes based on your tagging strategy
- Monitor policy effectiveness and adjust retention periods as needed

### Docker Authentication
- Use the provided login command for Docker authentication
- Consider using credential helpers for automated workflows
- Rotate access keys regularly for security
