# Helm Module Outputs

output "metrics_server_status" {
  description = "Status of the metrics server installation"
  value       = var.enable_metrics_server ? "installed" : "not_installed"
}

output "cluster_autoscaler_status" {
  description = "Status of the cluster autoscaler installation"
  value       = var.enable_cluster_autoscaler ? "installed" : "not_installed"
}

output "aws_load_balancer_controller_status" {
  description = "Status of the AWS Load Balancer Controller installation"
  value       = var.enable_aws_load_balancer_controller ? "installed" : "not_installed"
}

output "nginx_ingress_status" {
  description = "Status of the NGINX Ingress Controller installation"
  value       = var.enable_nginx_ingress ? "installed" : "not_installed"
}

output "cert_manager_status" {
  description = "Status of the Cert Manager installation"
  value       = var.enable_cert_manager ? "installed" : "not_installed"
}

output "efs_csi_driver_status" {
  description = "Status of the EFS CSI Driver installation"
  value       = var.enable_efs_csi_driver ? "installed" : "not_installed"
}

output "secrets_store_csi_driver_status" {
  description = "Status of the Secrets Store CSI Driver installation"
  value       = var.enable_secrets_store_csi_driver ? "installed" : "not_installed"
}
