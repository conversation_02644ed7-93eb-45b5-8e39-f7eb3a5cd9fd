# Security Module Variables

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "create_local_key_file" {
  description = "Whether to create local key files for easy access"
  type        = bool
  default     = false
}

variable "key_algorithm" {
  description = "Algorithm for the SSH key generation"
  type        = string
  default     = "RSA"
  validation {
    condition     = contains(["RSA", "ECDSA", "ED25519"], var.key_algorithm)
    error_message = "Key algorithm must be RSA, ECDSA, or ED25519."
  }
}

variable "rsa_bits" {
  description = "Number of bits for RSA key (only used if algorithm is RSA)"
  type        = number
  default     = 4096
  validation {
    condition     = contains([2048, 3072, 4096], var.rsa_bits)
    error_message = "RSA bits must be 2048, 3072, or 4096."
  }
}
