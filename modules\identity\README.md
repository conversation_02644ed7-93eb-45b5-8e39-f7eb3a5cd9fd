# Identity Module

This module manages all IAM roles, policies, and user access for the EKS infrastructure. It centralizes identity management for better security and maintainability.

## 🎯 Purpose

The Identity module provides:
- **EKS Service Roles**: Core roles for cluster and node group operation
- **CSI Driver Roles**: IAM roles for EBS and EFS CSI drivers with Pod Identity/IRSA support
- **User Access Management**: Developer and manager access with appropriate permissions
- **Security Best Practices**: Least privilege access and proper role separation

## 📁 Module Structure

```
modules/identity/
├── main.tf          # IAM roles, policies, and users
├── variables.tf     # Input variables and validation
├── outputs.tf       # Output values for other modules
└── README.md        # This documentation
```

## 🚀 Features

### Core EKS Roles
- **Cluster Service Role**: Allows EKS to manage cluster control plane
- **Node Group Role**: Enables worker nodes to join and operate in the cluster

### CSI Driver Support
- **EBS CSI Driver Role**: For persistent block storage with Pod Identity/IRSA
- **EFS CSI Driver Role**: For shared file storage with Pod Identity/IRSA
- **Flexible Authentication**: Supports both Pod Identity (modern) and IRSA (legacy)

### User Access Management
- **Developer Access**: Limited permissions for development work
- **Manager Access**: Admin role assumption for elevated operations
- **Programmatic Access**: Access keys for CLI/API usage

## 📋 Usage

### Basic Usage
```hcl
module "identity" {
  source = "./modules/identity"

  # Basic Configuration
  project_name  = "my-project"
  environment   = "dev"
  cluster_name  = "my-cluster"

  # CSI Driver Configuration
  enable_ebs_csi_driver = true
  enable_efs           = false

  # Access Configuration
  create_developer_user = true
  create_manager_role   = true

  # OIDC Configuration (for IRSA when Pod Identity is disabled)
  enable_pod_identity = true
  # oidc_provider_arn = "arn:aws:iam::123456789012:oidc-provider/..."
  # oidc_issuer_url   = "oidc.eks.region.amazonaws.com/id/..."

  common_tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### With IRSA (Legacy Mode)
```hcl
module "identity" {
  source = "./modules/identity"

  # Basic Configuration
  project_name  = "my-project"
  environment   = "prod"
  cluster_name  = "my-cluster"

  # Enable EFS with IRSA
  enable_efs          = true
  enable_pod_identity = false
  oidc_provider_arn   = module.oidc.provider_arn
  oidc_issuer_url     = module.oidc.issuer_url

  common_tags = var.common_tags
}
```

## 🔧 Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `project_name` | `string` | - | Name of the project |
| `environment` | `string` | - | Environment name (dev, staging, prod) |
| `cluster_name` | `string` | - | Name of the EKS cluster |
| `common_tags` | `map(string)` | `{}` | Common tags to apply to all resources |
| `enable_ebs_csi_driver` | `bool` | `true` | Enable EBS CSI driver IAM role creation |
| `enable_efs` | `bool` | `false` | Enable EFS CSI driver IAM role creation |
| `enable_pod_identity` | `bool` | `true` | Whether Pod Identity is enabled |
| `oidc_provider_arn` | `string` | `null` | ARN of the OIDC provider (for IRSA) |
| `oidc_issuer_url` | `string` | `null` | OIDC issuer URL (for IRSA) |
| `create_developer_user` | `bool` | `false` | Create developer IAM user and access |
| `create_manager_role` | `bool` | `false` | Create manager IAM role and access |

## 📤 Outputs

### Core Roles
- `cluster_service_role_arn` - ARN of the EKS cluster service role
- `node_group_role_arn` - ARN of the EKS node group role

### CSI Driver Roles
- `ebs_csi_driver_role_arn` - ARN of the EBS CSI Driver IAM role
- `efs_csi_driver_role_arn` - ARN of the EFS CSI Driver IAM role

### User Access
- `developer_user_arn` - ARN of the developer IAM user
- `manager_user_arn` - ARN of the manager IAM user
- `eks_admin_role_arn` - ARN of the EKS admin IAM role

### Summary Outputs
- `all_roles` - Map of all IAM roles created
- `all_users` - Map of all IAM users created

## 🔒 Security Considerations

### Role Separation
- Each component has its own dedicated role with minimal required permissions
- CSI driver roles use Pod Identity (preferred) or IRSA for secure authentication
- Manager access requires role assumption for elevated operations

### Access Keys
- Access keys are marked as sensitive outputs
- Consider using temporary credentials or IAM Identity Center instead
- Rotate access keys regularly in production environments

### Trust Policies
- Roles have restrictive trust policies limiting who can assume them
- Pod Identity roles can only be assumed by EKS pods
- Admin roles require explicit assumption by authorized users

## 🔗 Integration

This module is designed to work with:
- **EKS Module**: Consumes role ARNs for cluster and node group configuration
- **OIDC Module**: Provides OIDC provider details for IRSA authentication
- **Helm Module**: Uses CSI driver roles for storage provisioning

## 📝 Notes

- Pod Identity is the modern approach and is preferred over IRSA
- When using IRSA, ensure OIDC provider ARN and issuer URL are provided
- Access keys are created for programmatic access but consider alternatives
- All resources are properly tagged for cost allocation and management
