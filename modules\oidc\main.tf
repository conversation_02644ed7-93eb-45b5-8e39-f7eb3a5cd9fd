# =============================================================================
# OIDC MODULE - OPENID CONNECT PROVIDER FOR IRSA
# =============================================================================
# This module manages the OpenID Connect (OIDC) identity provider for EKS
# It enables IAM Roles for Service Accounts (IRSA) functionality

# -----------------------------------------------------------------------------
# DATA SOURCES
# -----------------------------------------------------------------------------

# Get the TLS certificate from the EKS cluster's OIDC issuer
# This certificate is used to establish trust between AWS IAM and the EKS cluster
data "tls_certificate" "eks" {
  count = var.enable_irsa ? 1 : 0
  url   = var.cluster_oidc_issuer_url
}

# -----------------------------------------------------------------------------
# OIDC IDENTITY PROVIDER
# -----------------------------------------------------------------------------

# Create the OIDC identity provider in AWS IAM
# This allows IAM roles to trust tokens issued by the EKS cluster
resource "aws_iam_openid_connect_provider" "eks" {
  count = var.enable_irsa ? 1 : 0

  # AWS STS is the client that will use this OIDC provider
  client_id_list = ["sts.amazonaws.com"]

  # Certificate thumbprint for trust verification
  # This ensures that only tokens from the legitimate EKS cluster are accepted
  thumbprint_list = [data.tls_certificate.eks[0].certificates[0].sha1_fingerprint]

  # OIDC issuer URL from the EKS cluster
  # This is the endpoint that issues JWT tokens for service accounts
  url = var.cluster_oidc_issuer_url

  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-eks-oidc"
      Description = "OIDC provider for EKS cluster IRSA functionality"
      Type        = "oidc-provider"
    }
  )
}

# -----------------------------------------------------------------------------
# OUTPUTS FOR INTEGRATION
# -----------------------------------------------------------------------------

# These outputs are used by other modules (like identity) to configure
# IAM roles that trust this OIDC provider
