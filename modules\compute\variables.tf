# Compute Module Variables (Bastion Host)

variable "vpc_id" {
  description = "VPC ID where bastion host will be created"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for bastion host instances (bastion will be deployed in the first subnet only)"
  type        = list(string)
}

variable "security_group_ids" {
  description = "List of security group IDs for bastion host instances"
  type        = list(string)
}

variable "instance_type" {
  description = "EC2 instance type for bastion host"
  type        = string
  default     = "t3.micro"
}

variable "instance_count" {
  description = "Number of bastion host instances to create"
  type        = number
  default     = 1
}

variable "key_name" {
  description = "EC2 Key Pair name for SSH access to bastion host"
  type        = string
  default     = null
}

variable "associate_public_ip_address" {
  description = "Whether to associate a public IP address with bastion host instances (should be false for private infrastructure)"
  type        = bool
  default     = false
}

variable "user_data_script" {
  description = "User data script for bastion host initialization"
  type        = string
  default     = ""
}

variable "enable_detailed_monitoring" {
  description = "Enable detailed monitoring for bastion host instances"
  type        = bool
  default     = false
}

variable "root_volume_size" {
  description = "Size of the root EBS volume in GB for bastion host"
  type        = number
  default     = 10
}

variable "root_volume_type" {
  description = "Type of the root EBS volume for bastion host"
  type        = string
  default     = "gp3"
}

variable "root_volume_encrypted" {
  description = "Whether to encrypt the root EBS volume for bastion host"
  type        = bool
  default     = true
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# AMI Configuration
variable "bastion_ami_id" {
  description = "AMI ID for bastion host instances. If not provided, will use latest Ubuntu 24.04 LTS"
  type        = string
  default     = null
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}