# Data source for latest Ubuntu 24.04 LTS AMI (used only when bastion_ami_id is not provided)
# Using Ubuntu 24.04 LTS (Noble Numbat) for latest LTS version
data "aws_ami" "ubuntu" {
  count       = var.bastion_ami_id == null ? 1 : 0
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-noble-24.04-amd64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "state"
    values = ["available"]
  }

  filter {
    name   = "architecture"
    values = ["x86_64"]
  }
}

# Local value to determine which AMI to use
locals {
  bastion_ami_id = var.bastion_ami_id != null ? var.bastion_ami_id : data.aws_ami.ubuntu[0].id
}

# Bastion Host EC2 Instances
resource "aws_instance" "bastion" {
  count = var.instance_count

  ami           = local.bastion_ami_id
  instance_type = var.instance_type
  key_name      = var.key_name

  subnet_id                   = var.subnet_ids[0] # Deploy all bastion instances in the first subnet only
  vpc_security_group_ids      = var.security_group_ids
  associate_public_ip_address = var.associate_public_ip_address

  user_data = var.user_data_script != "" ? var.user_data_script : file("${path.module}/startup-script.sh")

  monitoring = var.enable_detailed_monitoring

  root_block_device {
    volume_size           = var.root_volume_size
    volume_type           = var.root_volume_type
    encrypted             = var.root_volume_encrypted
    delete_on_termination = true

    tags = merge(
      var.common_tags,
      {
        Name = "${var.project_name}-${var.environment}-bastion-${count.index + 1}-volume"
      }
    )
  }

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-bastion-${count.index + 1}"
      Type = "bastion"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}