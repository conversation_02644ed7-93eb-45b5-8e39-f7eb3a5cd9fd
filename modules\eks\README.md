# EKS Module

This module creates a production-ready Amazon EKS (Elastic Kubernetes Service) cluster with comprehensive security, access management, and operational features. The module follows AWS best practices and is organized into separate files for better maintainability and clarity.

## 🏗️ Architecture Overview

This EKS module creates:
- **EKS Control Plane**: Managed Kubernetes API server, etcd, and control plane components
- **Worker Nodes**: Managed node groups with auto-scaling capabilities
- **Security**: IAM roles, RBAC integration, and Pod Identity for secure AWS service access
- **Storage**: EBS CSI driver for persistent volume support
- **Monitoring**: CloudWatch logging for cluster observability
- **Access Management**: Developer and manager roles with fine-grained permissions

## 📁 File Structure

- **main.tf**: Documentation and module overview
- **cluster.tf**: EKS cluster, CloudWatch logs, and OIDC provider configuration
- **nodes.tf**: EKS managed node groups with scaling and configuration
- **iam-roles.tf**: Core IAM roles for cluster and worker nodes
- **access-roles.tf**: User access roles (developers, managers) with RBAC integration
- **addons.tf**: EKS addons (Pod Identity, EBS CSI Driver) with IAM permissions
- **~~helm-provider.tf~~**: *(Removed - <PERSON><PERSON> logic moved to separate module)*
- **variables.tf**: Input variables and configuration options
- **outputs.tf**: Output values for integration with other modules

## 🚀 Features

### Core EKS Components
- **EKS Cluster**: Fully managed Kubernetes control plane with configurable version
- **Managed Node Groups**: Auto-scaling worker nodes with support for multiple instance types
- **CloudWatch Logging**: Comprehensive control plane logging (API, audit, authenticator, etc.)
- **VPC Integration**: Flexible networking with private/public endpoint configuration
- **High Availability**: Multi-AZ deployment support for both control plane and worker nodes

### 🔐 Security & Access Management
- **Modern Authentication**: EKS Access Entries API (replaces aws-auth ConfigMap)
- **Pod Identity**: AWS's latest approach for secure pod-to-AWS service authentication
- **IRSA Support**: IAM Roles for Service Accounts with OIDC provider (legacy support)
- **Role-Based Access**: Separate developer and manager roles with different permission levels
- **Fine-Grained RBAC**: Integration with Kubernetes Role-Based Access Control

### 💾 Storage & Networking
- **EBS CSI Driver**: Container Storage Interface for Amazon EBS persistent volumes
- **Encryption Support**: Optional EBS volume encryption with KMS integration
- **Flexible Networking**: Configurable subnets for control plane and worker nodes
- **Security Groups**: Automatic security group management for cluster communication

### 📊 Monitoring & Scaling
- **CloudWatch Integration**: Configurable log retention and monitoring
- **Auto Scaling**: Node group scaling with lifecycle management
- **Mixed Instances**: Support for On-Demand and Spot instances
- **Resource Tagging**: Comprehensive tagging for cost allocation and management

### 🔧 Operational Features
- **Addon Management**: AWS-managed addons with automatic updates
- **Version Management**: Coordinated cluster and node group version updates
- **Lifecycle Hooks**: Proper resource dependencies and cleanup
- **Terraform State**: Optimized for Terraform lifecycle management

## 🔐 Access Management & RBAC

### Understanding EKS Access Control

This module implements a modern, secure approach to EKS access management using **EKS Access Entries** instead of the traditional `aws-auth` ConfigMap. Here's how it works:

#### 1. Authentication (Who can access?)
- **AWS IAM**: Users/roles are authenticated by AWS IAM
- **EKS Access Entries**: Map IAM principals to Kubernetes groups
- **No ConfigMap**: Eliminates the need to manage the `aws-auth` ConfigMap

#### 2. Authorization (What can they do?)
- **Kubernetes RBAC**: Uses standard Kubernetes Roles and ClusterRoles
- **Group-Based**: IAM principals are mapped to Kubernetes groups
- **Fine-Grained**: Permissions controlled at namespace or cluster level

### Access Roles Explained

#### Developer Role
```
IAM User → EKS Access Entry → Kubernetes Groups → RBAC Permissions
```

**What it creates:**
- IAM User: `{cluster-name}-developer`
- IAM Policy: Basic EKS describe permissions
- EKS Access Entry: Maps user to Kubernetes groups
- **You must create**: ClusterRole/Role and RoleBinding for the groups

**Typical developer permissions:**
```yaml
# Example ClusterRole for developers (you create this)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: developer-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
# Bind the role to the group
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: developer-binding
subjects:
- kind: Group
  name: "my-developers"  # This matches var.developer_kubernetes_groups
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: ClusterRole
  name: developer-role
  apiGroup: rbac.authorization.k8s.io
```

#### Manager Role
```
IAM User → Assume IAM Role → EKS Access Entry → Kubernetes Groups → RBAC Permissions
```

**What it creates:**
- IAM User: `{cluster-name}-manager`
- IAM Role: `{cluster-name}-eks-admin` (with EKS permissions)
- IAM Policy: Allows user to assume the admin role
- EKS Access Entry: Maps admin role to Kubernetes groups
- **You must create**: ClusterRole/Role and RoleBinding for the groups

**Why use a role for managers?**
- **Temporary Credentials**: Managers assume role when needed
- **Audit Trail**: All actions logged with role assumption
- **Principle of Least Privilege**: Users don't have permanent admin access

### Setting Up RBAC (Manual Steps Required)

After deploying this module, you **must** create Kubernetes RBAC resources:

#### 1. Create ClusterRoles
```bash
kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: developer-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]
EOF
```

#### 2. Create RoleBindings
```bash
kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: developer-binding
subjects:
- kind: Group
  name: "my-developers"  # Match your variable
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: ClusterRole
  name: developer-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: manager-binding
subjects:
- kind: Group
  name: "my-managers"  # Match your variable
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: ClusterRole
  name: manager-role
  apiGroup: rbac.authorization.k8s.io
EOF
```

### User Access Instructions

#### For Developers:
1. **Get AWS credentials** for the developer IAM user
2. **Configure kubectl**:
   ```bash
   aws eks update-kubeconfig --region <region> --name <cluster-name>
   ```
3. **Test access**:
   ```bash
   kubectl get pods --all-namespaces
   ```

#### For Managers:
1. **Get AWS credentials** for the manager IAM user
2. **Assume the admin role**:
   ```bash
   aws sts assume-role --role-arn <admin-role-arn> --role-session-name manager-session
   ```
3. **Configure kubectl** with assumed role credentials
4. **Test admin access**:
   ```bash
   kubectl get nodes
   ```

## 📋 Usage

```hcl
module "eks" {
  source = "./modules/eks"

  # Basic Configuration
  cluster_name    = "my-cluster"
  cluster_version = "1.28"
  project_name    = "my-project"
  environment     = "dev"

  # Network Configuration
  vpc_id                 = module.vpc.vpc_id
  subnet_ids             = module.vpc.private_subnet_ids
  node_group_subnet_ids  = module.vpc.private_subnet_ids

  # Access Configuration
  endpoint_private_access = true
  endpoint_public_access  = false

  # Node Groups
  node_groups = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size       = 1
      max_size       = 5
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels = {
        role = "general"
      }
      taints = []
    }
    spot = {
      instance_types = ["t3.medium", "t3.large"]
      capacity_type  = "SPOT"
      min_size       = 0
      max_size       = 10
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels = {
        role = "spot"
      }
      taints = [
        {
          key    = "spot"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }

  # Access Management
  create_developer_user = true
  create_manager_role   = true

  # Addons
  enable_pod_identity    = true
  enable_ebs_csi_driver = true
  enable_ebs_encryption = true

  # Logging
  enable_cluster_log_types     = ["api", "audit", "authenticator"]
  cluster_log_retention_days   = 14

  common_tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

## 🔧 External Setup Requirements

### AWS Console Setup (If Required)

Most configuration is handled by Terraform, but some optional manual steps may be needed:

#### 1. KMS Key for EBS Encryption (Optional)
If you enable EBS encryption (`enable_ebs_encryption = true`), you may want to create a custom KMS key:

```bash
# Create KMS key via AWS CLI
aws kms create-key \
  --description "EKS EBS encryption key for ${cluster_name}" \
  --key-usage ENCRYPT_DECRYPT \
  --key-spec SYMMETRIC_DEFAULT

# Create alias for the key
aws kms create-alias \
  --alias-name alias/${cluster_name}-ebs \
  --target-key-id <key-id-from-above>
```

#### 2. VPC and Networking Prerequisites
Ensure your VPC has:
- **Private subnets** for worker nodes (recommended)
- **Public subnets** for load balancers (if using ALB/NLB)
- **NAT Gateway** or **VPC Endpoints** for private subnet internet access
- **Proper CIDR blocks** that don't conflict with Kubernetes pod/service CIDRs

#### 3. IAM Permissions for Terraform
The Terraform execution role needs these permissions:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "eks:*",
        "iam:*",
        "ec2:*",
        "logs:*",
        "kms:*"
      ],
      "Resource": "*"
    }
  ]
}
```

### Post-Deployment Manual Steps

#### 1. Configure kubectl Access
```bash
# Update kubeconfig
aws eks update-kubeconfig --region <region> --name <cluster-name>

# Verify access
kubectl get nodes
```

#### 2. Create RBAC Resources (Required)
```bash
# Apply the RBAC configurations shown in the Access Management section above
kubectl apply -f rbac-config.yaml
```

#### 3. Install Additional Tools (Optional)
```bash
# Install Kubernetes Dashboard
kubectl apply -f https://raw.githubusercontent.com/kubernetes/dashboard/v2.7.0/aio/deploy/recommended.yaml

# Install Helm (if not already installed)
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

## 🚀 Helm Module Integration

This EKS module is designed to work with the companion Helm module for installing Kubernetes applications. The Helm module handles its own provider configuration and should be deployed after the EKS cluster is ready.

### Separate Helm Module Usage
```hcl
# First, deploy the EKS cluster
module "eks" {
  source = "./modules/eks"
  # ... EKS configuration
}

# Then, deploy Helm applications
module "helm" {
  source = "./modules/helm"

  # Cluster connection information
  cluster_name               = module.eks.cluster_name
  cluster_endpoint           = module.eks.cluster_endpoint
  cluster_ca_certificate     = module.eks.cluster_certificate_authority_data
  cluster_token              = data.aws_eks_cluster_auth.main.token

  # AWS and project configuration
  vpc_id       = module.vpc.vpc_id
  aws_region   = var.aws_region
  project_name = var.project_name
  environment  = var.environment
  common_tags  = var.common_tags

  # Enable desired components
  enable_metrics_server              = true
  enable_cluster_autoscaler          = true
  enable_aws_load_balancer_controller = true

  # Ensure EKS cluster is ready before deploying Helm charts
  depends_on = [module.eks]
}

# Authentication data source for Helm module
data "aws_eks_cluster_auth" "main" {
  name = module.eks.cluster_name
}
```

### Why Separate Modules?
- **Separation of Concerns**: EKS infrastructure vs. Kubernetes applications
- **Independent Lifecycle**: Update Helm charts without affecting EKS cluster
- **Provider Isolation**: Each module manages its own provider configuration
- **Easier Troubleshooting**: Clear boundaries between infrastructure and applications

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | >= 5.0 |
| tls | >= 4.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.0 |
| tls | >= 4.0 |

## Inputs

See `variables.tf` for a complete list of input variables.

## Outputs

See `outputs.tf` for a complete list of output values.

## Notes

- The module uses the new EKS access entries API for managing cluster access
- Pod Identity is preferred over IRSA for new deployments
- Node groups support both On-Demand and Spot instances
- All resources are tagged with common tags for cost tracking and management
