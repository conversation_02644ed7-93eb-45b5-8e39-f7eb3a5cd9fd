# Helm Module Variables

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_endpoint" {
  description = "EKS cluster endpoint"
  type        = string
}

variable "cluster_ca_certificate" {
  description = "EKS cluster CA certificate"
  type        = string
}

variable "cluster_token" {
  description = "EKS cluster authentication token"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where the cluster is deployed"
  type        = string
}

variable "aws_region" {
  description = "AWS region"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

# Metrics Server Configuration
variable "enable_metrics_server" {
  description = "Enable metrics server installation"
  type        = bool
  default     = true
}

variable "metrics_server_version" {
  description = "Version of metrics server to install"
  type        = string
  default     = "3.12.1"
}

# Cluster Autoscaler Configuration
variable "enable_cluster_autoscaler" {
  description = "Enable cluster autoscaler installation"
  type        = bool
  default     = true
}

variable "cluster_autoscaler_version" {
  description = "Version of cluster autoscaler to install"
  type        = string
  default     = "9.37.0"
}

variable "cluster_autoscaler_role_arn" {
  description = "IAM role ARN for cluster autoscaler"
  type        = string
  default     = ""
}

# AWS Load Balancer Controller Configuration
variable "enable_aws_load_balancer_controller" {
  description = "Enable AWS Load Balancer Controller installation"
  type        = bool
  default     = true
}

variable "aws_load_balancer_controller_version" {
  description = "Version of AWS Load Balancer Controller to install"
  type        = string
  default     = "1.8.1"
}

variable "aws_load_balancer_controller_role_arn" {
  description = "IAM role ARN for AWS Load Balancer Controller"
  type        = string
  default     = ""
}

# NGINX Ingress Controller Configuration
variable "enable_nginx_ingress" {
  description = "Enable NGINX Ingress Controller installation"
  type        = bool
  default     = false
}

variable "nginx_ingress_version" {
  description = "Version of NGINX Ingress Controller to install"
  type        = string
  default     = "4.10.1"
}

# Cert Manager Configuration
variable "enable_cert_manager" {
  description = "Enable Cert Manager installation"
  type        = bool
  default     = false
}

variable "cert_manager_version" {
  description = "Version of Cert Manager to install"
  type        = string
  default     = "v1.15.0"
}

# EFS CSI Driver Configuration
variable "enable_efs_csi_driver" {
  description = "Enable EFS CSI Driver installation"
  type        = bool
  default     = false
}

variable "efs_csi_driver_version" {
  description = "Version of EFS CSI Driver to install"
  type        = string
  default     = "3.0.5"
}

variable "efs_csi_driver_role_arn" {
  description = "IAM role ARN for EFS CSI Driver"
  type        = string
  default     = ""
}

# Secrets Store CSI Driver Configuration
variable "enable_secrets_store_csi_driver" {
  description = "Enable Secrets Store CSI Driver installation"
  type        = bool
  default     = false
}

variable "secrets_store_csi_driver_version" {
  description = "Version of Secrets Store CSI Driver to install"
  type        = string
  default     = "1.4.3"
}

variable "secrets_store_csi_driver_aws_provider_version" {
  description = "Version of Secrets Store CSI Driver AWS Provider to install"
  type        = string
  default     = "0.3.9"
}
