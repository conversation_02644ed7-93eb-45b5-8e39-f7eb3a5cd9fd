# =============================================================================
# HELM MODULE PROVIDERS CONFIGURATION
# =============================================================================
# This file configures the Helm and Kubernetes providers for the Helm module
# These providers are separate from the EKS module to maintain proper
# separation of concerns and avoid provider conflicts

# -----------------------------------------------------------------------------
# TERRAFORM PROVIDER REQUIREMENTS
# -----------------------------------------------------------------------------
# Specifies the required provider versions for this module
terraform {
  required_providers {
    # Helm provider for deploying Helm charts
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.12" # Use latest 2.x version for stability
    }
    # Kubernetes provider for direct K8s API operations
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.24" # Compatible with recent Kubernetes versions
    }
  }
}

# -----------------------------------------------------------------------------
# PROVIDER CONFIGURATION NOTES
# -----------------------------------------------------------------------------
# This module expects Helm and Kubernetes providers to be configured by the
# calling module. This allows the module to be used with count, for_each,
# and depends_on arguments.
#
# The calling module should configure providers like this:
#
# provider "helm" {
#   kubernetes {
#     host                   = module.eks[0].cluster_endpoint
#     cluster_ca_certificate = base64decode(module.eks[0].cluster_ca_certificate)
#     token                  = data.aws_eks_cluster_auth.cluster.token
#   }
# }
#
# provider "kubernetes" {
#   host                   = module.eks[0].cluster_endpoint
#   cluster_ca_certificate = base64decode(module.eks[0].cluster_ca_certificate)
#   token                  = data.aws_eks_cluster_auth.cluster.token
# }
