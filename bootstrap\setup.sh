#!/bin/bash

# Bootstrap Setup Script
# This script helps set up the Terraform backend infrastructure

set -e

echo "🚀 Terraform Backend Bootstrap Setup"
echo "====================================="

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI is not configured or credentials are invalid"
    echo "Please run: aws configure"
    exit 1
fi

echo "✅ AWS CLI is configured"

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform is not installed"
    echo "Please install Terraform >= 1.0"
    exit 1
fi

echo "✅ Terraform is installed"

# Check Terraform version
TF_VERSION=$(terraform version -json | jq -r '.terraform_version')
echo "📋 Terraform version: $TF_VERSION"

# Initialize Terraform
echo ""
echo "🔧 Initializing Terraform..."
terraform init

# Show the plan
echo ""
echo "📋 Planning deployment..."
terraform plan

# Ask for confirmation
echo ""
echo "⚠️  This will create:"
echo "   - S3 bucket for Terraform state storage"
echo "   - DynamoDB table for state locking"
echo "   - All necessary security configurations"
echo ""
read -p "Do you want to proceed? (yes/no): " confirmation

if [ "$confirmation" = "yes" ]; then
    echo ""
    echo "🚀 Applying bootstrap configuration..."
    terraform apply -auto-approve
    
    echo ""
    echo "✅ Bootstrap completed successfully!"
    echo ""
    echo "📋 Backend Configuration:"
    terraform output backend_config_hcl
    
    echo ""
    echo "🎉 Your Terraform backend is ready!"
    echo "You can now use the main Terraform project."
    echo ""
    echo "Next steps:"
    echo "1. cd .."
    echo "2. terraform init"
    echo "3. terraform plan -var-file=dev.tfvars"
    echo "4. terraform apply -var-file=dev.tfvars"
else
    echo ""
    echo "🛑 Setup cancelled."
fi 