# =============================================================================
# ECR REGISTRY MODULE - CONTAINER REGISTRIES
# =============================================================================
# This module manages Amazon Elastic Container Registry (ECR) repositories
# for storing container images used in GitOps workflows and application deployments

# -----------------------------------------------------------------------------
# DATA SOURCES
# -----------------------------------------------------------------------------

# Get current AWS account ID for constructing ARNs and policies
data "aws_caller_identity" "current" {}

# Get current AWS region
data "aws_region" "current" {}

# -----------------------------------------------------------------------------
# ECR REPOSITORIES
# -----------------------------------------------------------------------------

# Create ECR repositories based on the provided list
resource "aws_ecr_repository" "repositories" {
  for_each = toset(var.repository_names)

  name = "${var.project_name}-${var.environment}-${each.value}"

  # Image scanning configuration
  image_scanning_configuration {
    scan_on_push = var.enable_image_scanning
  }

  # Image tag mutability
  image_tag_mutability = var.image_tag_mutability

  # Encryption configuration
  encryption_configuration {
    encryption_type = var.encryption_type
    kms_key         = var.encryption_type == "KMS" ? var.kms_key_id : null
  }

  tags = merge(
    var.common_tags,
    {
      Name       = "${var.project_name}-${var.environment}-${each.value}"
      Repository = each.value
      Type       = "ecr-repository"
    }
  )
}

# -----------------------------------------------------------------------------
# LIFECYCLE POLICIES
# -----------------------------------------------------------------------------

# Apply lifecycle policies to manage image retention and cost optimization
resource "aws_ecr_lifecycle_policy" "repositories" {
  for_each   = var.enable_lifecycle_policy ? toset(var.repository_names) : []
  repository = aws_ecr_repository.repositories[each.value].name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last ${var.max_image_count} images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = var.lifecycle_tag_prefixes
          countType     = "imageCountMoreThan"
          countNumber   = var.max_image_count
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 2
        description  = "Delete untagged images older than ${var.untagged_image_days} days"
        selection = {
          tagStatus   = "untagged"
          countType   = "sinceImagePushed"
          countUnit   = "days"
          countNumber = var.untagged_image_days
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# -----------------------------------------------------------------------------
# REPOSITORY POLICIES
# -----------------------------------------------------------------------------

# Create repository policies for cross-account access if specified
resource "aws_ecr_repository_policy" "repositories" {
  for_each   = var.enable_cross_account_access ? toset(var.repository_names) : []
  repository = aws_ecr_repository.repositories[each.value].name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowCrossAccountPull"
        Effect = "Allow"
        Principal = {
          AWS = var.cross_account_arns
        }
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability"
        ]
      }
    ]
  })
}
