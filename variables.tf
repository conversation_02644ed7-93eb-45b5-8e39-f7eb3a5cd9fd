# =============================================================================
# GLOBAL CONFIGURATION VARIABLES
# =============================================================================

# AWS Region
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "eu-west-1"
}

# Project Name
variable "project_name" {
  description = "Project name for resource naming and tagging"
  type        = string
  default     = "eks-infra"
}

# Environment
variable "environment" {
  description = "Environment name for tagging"
  type        = string
  default     = "dev"
}

# =============================================================================
# NETWORK CONFIGURATION VARIABLES
# =============================================================================

# VPC CIDR Block
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "**********/16"
}

# VPC Availability Zones
variable "availability_zones" {
  description = "List of availability zones for subnets"
  type        = list(string)
  default     = ["eu-west-1a", "eu-west-1b"]
}

# Transit Gateway Attachment Subnet CIDRs
variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
  default = [
    "**********/26", # TGW Subnet 1
    "**********/26"  # TGW Subnet 2
  ]
}

# Private EKS Subnet CIDRs
variable "private_eks_subnet_cidrs" {
  description = "CIDR blocks for private EKS subnets"
  type        = map(string)
  default = {
    az1 = "***********/24"
    az2 = "***********/24"
  }
}

# Private Load Balancer Subnet CIDRs
variable "private_lb_subnet_cidrs" {
  description = "CIDR blocks for private load balancer subnets"
  type        = map(string)
  default = {
    az1 = "***********/24"
    az2 = "***********/24"
  }
}

# Intra (Isolated) Subnet CIDRs
variable "intra_subnet_cidrs" {
  description = "CIDR blocks for intra (isolated) subnets"
  type        = map(string)
  default = {
    az1 = "***********/24"
    az2 = "***********/24"
  }
}

# Map Public IP on Launch
variable "map_public_ip_on_launch" {
  description = "Auto-assign public IP on launch for subnets"
  type        = bool
  default     = false
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform  = "true"
    Owner      = "DevOps Team"
    CostCenter = "Engineering"
  }
}

# =============================================================================
# MODULE ENABLE/DISABLE CONTROLS
# =============================================================================
# Simple module controls for staged deployment

variable "enable_network_module" {
  description = "Enable network module (VPC, subnets, security groups)"
  type        = bool
  default     = true
}

variable "enable_compute_module" {
  description = "Enable compute module (bastion hosts)"
  type        = bool
  default     = true
}

variable "enable_eks_module" {
  description = "Enable EKS module deployment"
  type        = bool
  default     = true
}

# Transit Gateway Configuration
variable "existing_transit_gateway_id" {
  description = "ID of existing Transit Gateway to attach VPC to (if not creating new one)"
  type        = string
  default     = null
}

variable "create_transit_gateway_attachment" {
  description = "Whether to create a Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "enable_dns_support_attachment" {
  description = "Whether DNS support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = true
}

variable "enable_ipv6_support_attachment" {
  description = "Whether IPv6 support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "appliance_mode_support" {
  description = "Whether Appliance Mode support is enabled for the Transit Gateway VPC attachment"
  type        = string
  default     = "disable"
  validation {
    condition     = contains(["enable", "disable"], var.appliance_mode_support)
    error_message = "Appliance mode support must be either 'enable' or 'disable'."
  }
}

# Security Module Variables
variable "create_local_key_files" {
  description = "Whether to create local SSH key files for easy access"
  type        = bool
  default     = false
}

variable "bastion_key_algorithm" {
  description = "Algorithm for the SSH key generation"
  type        = string
  default     = "RSA"
  validation {
    condition     = contains(["RSA", "ECDSA", "ED25519"], var.bastion_key_algorithm)
    error_message = "Key algorithm must be RSA, ECDSA, or ED25519."
  }
}

variable "bastion_rsa_bits" {
  description = "Number of bits for RSA key (only used if algorithm is RSA)"
  type        = number
  default     = 4096
  validation {
    condition     = contains([2048, 3072, 4096], var.bastion_rsa_bits)
    error_message = "RSA bits must be 2048, 3072, or 4096."
  }
}

# Bastion Host Module Variables (now controlled by enable_compute_module)

variable "bastion_instance_type" {
  description = "EC2 instance type for bastion host"
  type        = string
  default     = "t3.micro"
}

variable "bastion_instance_count" {
  description = "Number of bastion host instances to create"
  type        = number
  default     = 1
}

# bastion_key_name is now automatically generated by the security module

variable "bastion_associate_public_ip" {
  description = "Whether to associate a public IP address with bastion host (should be false for private infrastructure)"
  type        = bool
  default     = false
}

variable "bastion_root_volume_size" {
  description = "Size of the root EBS volume for bastion host in GB"
  type        = number
  default     = 10
}

variable "bastion_ami_id" {
  description = "AMI ID for bastion host instances. If not provided, will use latest Ubuntu 24.04 LTS"
  type        = string
  default     = null
}

# =============================================================================
# EKS CLUSTER CONFIGURATION VARIABLES
# =============================================================================

variable "eks_cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = null
}

variable "eks_cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "eks_endpoint_private_access" {
  description = "Enable private API server endpoint for EKS"
  type        = bool
  default     = true
}

variable "eks_endpoint_public_access" {
  description = "Enable public API server endpoint for EKS"
  type        = bool
  default     = true
}

variable "eks_endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "eks_enable_cluster_log_types" {
  description = "List of control plane logging types to enable for EKS"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "eks_cluster_log_retention_days" {
  description = "Number of days to retain EKS cluster logs"
  type        = number
  default     = 7
}

variable "eks_node_groups" {
  description = "Map of EKS node group configurations"
  type = map(object({
    instance_types = list(string)
    capacity_type  = string
    min_size       = number
    max_size       = number
    desired_size   = number
    disk_size      = number
    ami_type       = string
    labels         = map(string)
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
  }))
  default = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size       = 1
      max_size       = 3
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels         = {}
      taints         = []
    }
  }
}

variable "eks_enable_irsa" {
  description = "Enable IAM Roles for Service Accounts for EKS"
  type        = bool
  default     = true
}

# =============================================================================
# EKS AUTHENTICATION AND ACCESS VARIABLES
# =============================================================================

variable "eks_enable_pod_identity" {
  description = "Enable Pod Identity for EKS (newer alternative to IRSA)"
  type        = bool
  default     = true
}

variable "eks_enable_ebs_csi_driver" {
  description = "Enable EBS CSI driver addon for EKS"
  type        = bool
  default     = true
}

variable "eks_create_developer_user" {
  description = "Create developer IAM user with EKS access"
  type        = bool
  default     = false
}

variable "eks_create_manager_role" {
  description = "Create manager IAM role with EKS admin access"
  type        = bool
  default     = false
}

variable "eks_developer_kubernetes_groups" {
  description = "Kubernetes groups for developer access"
  type        = list(string)
  default     = ["my-viewer"]
}

variable "eks_manager_kubernetes_groups" {
  description = "Kubernetes groups for manager access"
  type        = list(string)
  default     = ["my-admin"]
}

# =============================================================================
# ECR REGISTRY CONFIGURATION VARIABLES
# =============================================================================

variable "enable_ecr_registry" {
  description = "Enable ECR registry module"
  type        = bool
  default     = false
}

variable "ecr_repository_names" {
  description = "List of ECR repository names to create"
  type        = list(string)
  default     = ["app", "api", "frontend"]
}

variable "ecr_image_tag_mutability" {
  description = "Image tag mutability setting for ECR repositories"
  type        = string
  default     = "MUTABLE"
  validation {
    condition     = contains(["MUTABLE", "IMMUTABLE"], var.ecr_image_tag_mutability)
    error_message = "Image tag mutability must be either MUTABLE or IMMUTABLE."
  }
}

variable "ecr_enable_image_scanning" {
  description = "Enable image scanning for ECR repositories"
  type        = bool
  default     = true
}

variable "ecr_encryption_type" {
  description = "Encryption type for ECR repositories"
  type        = string
  default     = "AES256"
  validation {
    condition     = contains(["AES256", "KMS"], var.ecr_encryption_type)
    error_message = "Encryption type must be either AES256 or KMS."
  }
}

variable "ecr_kms_key_id" {
  description = "KMS key ID for ECR encryption (only used when encryption_type is KMS)"
  type        = string
  default     = null
}

variable "ecr_enable_lifecycle_policy" {
  description = "Enable lifecycle policy for ECR repositories"
  type        = bool
  default     = true
}

variable "ecr_max_image_count" {
  description = "Maximum number of images to keep in ECR repositories"
  type        = number
  default     = 10
}

variable "ecr_untagged_image_days" {
  description = "Number of days to keep untagged images"
  type        = number
  default     = 1
}

variable "ecr_lifecycle_tag_prefixes" {
  description = "Tag prefixes for lifecycle policy rules"
  type        = list(string)
  default     = ["v", "release"]
}

variable "ecr_enable_cross_account_access" {
  description = "Enable cross-account access for ECR repositories"
  type        = bool
  default     = false
}

variable "ecr_cross_account_arns" {
  description = "List of cross-account ARNs for ECR access"
  type        = list(string)
  default     = []
}

# =============================================================================
# HELM MODULE VARIABLES
# =============================================================================

variable "enable_helm_module" {
  description = "Enable Helm module for deploying Kubernetes applications"
  type        = bool
  default     = true
}

# -----------------------------------------------------------------------------
# METRICS SERVER CONFIGURATION
# -----------------------------------------------------------------------------
variable "enable_metrics_server" {
  description = "Enable metrics server installation"
  type        = bool
  default     = true
}

variable "metrics_server_version" {
  description = "Version of metrics server to install"
  type        = string
  default     = "3.12.1"
}

# -----------------------------------------------------------------------------
# CLUSTER AUTOSCALER CONFIGURATION
# -----------------------------------------------------------------------------
variable "enable_cluster_autoscaler" {
  description = "Enable cluster autoscaler installation"
  type        = bool
  default     = true
}

variable "cluster_autoscaler_version" {
  description = "Version of cluster autoscaler to install"
  type        = string
  default     = "9.37.0"
}

variable "cluster_autoscaler_role_arn" {
  description = "IAM role ARN for cluster autoscaler"
  type        = string
  default     = ""
}

# -----------------------------------------------------------------------------
# AWS LOAD BALANCER CONTROLLER CONFIGURATION
# -----------------------------------------------------------------------------
variable "enable_aws_load_balancer_controller" {
  description = "Enable AWS Load Balancer Controller installation"
  type        = bool
  default     = true
}

variable "aws_load_balancer_controller_version" {
  description = "Version of AWS Load Balancer Controller to install"
  type        = string
  default     = "1.8.1"
}

variable "aws_load_balancer_controller_role_arn" {
  description = "IAM role ARN for AWS Load Balancer Controller"
  type        = string
  default     = ""
}

# -----------------------------------------------------------------------------
# NGINX INGRESS CONTROLLER CONFIGURATION
# -----------------------------------------------------------------------------
variable "enable_nginx_ingress" {
  description = "Enable NGINX Ingress Controller installation"
  type        = bool
  default     = false
}

variable "nginx_ingress_version" {
  description = "Version of NGINX Ingress Controller to install"
  type        = string
  default     = "4.10.1"
}

# -----------------------------------------------------------------------------
# CERT MANAGER CONFIGURATION
# -----------------------------------------------------------------------------
variable "enable_cert_manager" {
  description = "Enable Cert Manager installation"
  type        = bool
  default     = false
}

variable "cert_manager_version" {
  description = "Version of Cert Manager to install"
  type        = string
  default     = "v1.15.0"
}

# =============================================================================
# EFS (ELASTIC FILE SYSTEM) CONFIGURATION
# =============================================================================

variable "enable_efs" {
  description = "Enable EFS file system and CSI driver"
  type        = bool
  default     = false
}

variable "efs_performance_mode" {
  description = "EFS performance mode (generalPurpose or maxIO)"
  type        = string
  default     = "generalPurpose"

  validation {
    condition     = contains(["generalPurpose", "maxIO"], var.efs_performance_mode)
    error_message = "EFS performance mode must be either 'generalPurpose' or 'maxIO'."
  }
}

variable "efs_throughput_mode" {
  description = "EFS throughput mode (bursting or provisioned)"
  type        = string
  default     = "bursting"

  validation {
    condition     = contains(["bursting", "provisioned"], var.efs_throughput_mode)
    error_message = "EFS throughput mode must be either 'bursting' or 'provisioned'."
  }
}

variable "efs_encrypted" {
  description = "Enable EFS encryption at rest"
  type        = bool
  default     = true
}

variable "efs_transition_to_ia" {
  description = "Lifecycle policy transition to Infrequent Access (AFTER_7_DAYS, AFTER_14_DAYS, AFTER_30_DAYS, AFTER_60_DAYS, AFTER_90_DAYS)"
  type        = string
  default     = null

  validation {
    condition = var.efs_transition_to_ia == null || contains([
      "AFTER_7_DAYS", "AFTER_14_DAYS", "AFTER_30_DAYS",
      "AFTER_60_DAYS", "AFTER_90_DAYS"
    ], var.efs_transition_to_ia)
    error_message = "EFS transition to IA must be one of: AFTER_7_DAYS, AFTER_14_DAYS, AFTER_30_DAYS, AFTER_60_DAYS, AFTER_90_DAYS, or null."
  }
}

variable "efs_csi_driver_chart_version" {
  description = "Version of the EFS CSI driver Helm chart"
  type        = string
  default     = "3.0.5"
}

variable "efs_set_default_storage_class" {
  description = "Set EFS storage class as default"
  type        = bool
  default     = false
}

variable "efs_directory_perms" {
  description = "Directory permissions for EFS access points"
  type        = string
  default     = "700"
}

variable "efs_mount_options" {
  description = "Mount options for EFS volumes"
  type        = list(string)
  default     = ["iam"]
}

variable "efs_reclaim_policy" {
  description = "Reclaim policy for EFS volumes (Retain, Delete)"
  type        = string
  default     = "Retain"

  validation {
    condition     = contains(["Retain", "Delete"], var.efs_reclaim_policy)
    error_message = "EFS reclaim policy must be either 'Retain' or 'Delete'."
  }
}

# -----------------------------------------------------------------------------
# SECRETS STORE CSI DRIVER CONFIGURATION
# -----------------------------------------------------------------------------
variable "enable_secrets_store_csi_driver" {
  description = "Enable Secrets Store CSI Driver installation"
  type        = bool
  default     = false
}

variable "secrets_store_csi_driver_version" {
  description = "Version of Secrets Store CSI Driver to install"
  type        = string
  default     = "1.4.3"
}

variable "secrets_store_csi_driver_aws_provider_version" {
  description = "Version of Secrets Store CSI Driver AWS Provider to install"
  type        = string
  default     = "0.3.9"
}
