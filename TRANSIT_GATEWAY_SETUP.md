# Transit Gateway Setup

> **Note:** All Transit Gateway logic is now managed in the modular `network` module. See [`modules/network/README.md`](modules/network/README.md) for implementation details and usage.

This guide explains how to configure the VPC to connect to an existing Transit Gateway.

## Overview

This Terraform configuration creates a VPC attachment to an **existing Transit Gateway** that was created manually outside of Terraform. The setup includes:

- **VPC Attachment**: Connects your VPC to the existing Transit Gateway
- **Dedicated Subnets**: Uses 2 dedicated subnets for the Transit Gateway attachment
- **Route Configuration**: Optional routing through the Transit Gateway
- **DNS Support**: Configurable DNS resolution through the Transit Gateway

## Prerequisites

1. **Existing Transit Gateway**: You must have a Transit Gateway already created in your AWS account
2. **Transit Gateway ID**: You need the Transit Gateway ID (format: `tgw-xxxxxxxxxxxxxxxxx`)
3. **Same Region**: The Transit Gateway must be in the same AWS region as your VPC
4. **Permissions**: Your AWS account must have permissions to create VPC attachments

## Configuration Steps

### Step 1: Get Your Transit Gateway ID

Find your existing Transit Gateway ID using one of these methods:

**AWS CLI:**
```bash
aws ec2 describe-transit-gateways --region your-region
```

**AWS Console:**
1. Go to VPC Console → Transit Gateways
2. Copy the Transit Gateway ID from the list

### Step 2: Configure Variables

Edit your `terraform.tfvars` file and set the Transit Gateway configuration:

```hcl
# Transit Gateway Configuration
existing_transit_gateway_id = "tgw-0123456789abcdef0"  # Replace with your TGW ID

# Transit Gateway Attachment Configuration
create_transit_gateway_attachment = true
enable_dns_support_attachment      = true
enable_ipv6_support_attachment     = false
appliance_mode_support            = "disable"
```

### Step 3: Subnet Configuration

The Transit Gateway attachment uses dedicated subnets defined in `tgw_subnet_cidrs`:

```hcl
# Transit Gateway Attachment Subnets
tgw_subnet_cidrs = [
  "**********/26",  # TGW Subnet 1 - AZ 1
  "**********/26"   # TGW Subnet 2 - AZ 2
]
```

**Important Notes:**
- Use smaller subnets (/26 or /27) for TGW attachments as they don't need many IPs
- Subnets should be in different Availability Zones for high availability
- Ensure the CIDR blocks don't overlap with other networks connected to the TGW

## Configuration Options

### DNS Support
- **Enabled**: Allows DNS resolution between VPCs connected to the Transit Gateway
- **Disabled**: No DNS resolution through the Transit Gateway

### IPv6 Support
- **Enabled**: Supports IPv6 traffic through the Transit Gateway
- **Disabled**: IPv4 traffic only

### Appliance Mode Support
- **Enabled**: Routes traffic symmetrically for network appliances
- **Disabled**: Standard routing behavior

## Deployment

1. **Initialize Terraform:**
   ```bash
   terraform init
   ```

2. **Plan the deployment:**
   ```bash
   terraform plan
   ```

3. **Apply the configuration:**
   ```bash
   terraform apply
   ```

## Verification

After deployment, verify the attachment:

**AWS CLI:**
```bash
# List VPC attachments
aws ec2 describe-transit-gateway-vpc-attachments --region your-region

# Check attachment state
aws ec2 describe-transit-gateway-vpc-attachments \
  --transit-gateway-attachment-ids tgw-attach-xxxxxxxxx \
  --region your-region
```

**Expected State:** `available`

## Routing Configuration

### Automatic Routing
The configuration includes optional automatic routing:

```hcl
# Route all traffic through Transit Gateway
destination_cidr_block = "0.0.0.0/0"
```

### Custom Routing
You can modify the routing in `transit_gateway_attachment.tf`:

```hcl
# Route specific networks through TGW
destination_cidr_block = "10.0.0.0/8"  # Only private networks
```

### Route Table Configuration
The TGW attachment subnets have their own route table separate from general use subnets.

## Troubleshooting

### Common Issues

1. **Invalid Transit Gateway ID**
   - Verify the TGW ID format: `tgw-xxxxxxxxxxxxxxxxx`
   - Ensure the TGW exists in the same region

2. **Permission Denied**
   - Check IAM permissions for `ec2:CreateTransitGatewayVpcAttachment`
   - Verify cross-account permissions if TGW is in different account

3. **Subnet Overlap**
   - Ensure TGW subnet CIDRs don't overlap with existing networks
   - Check other VPCs connected to the same Transit Gateway

4. **Attachment Stuck in Pending**
   - Check Transit Gateway route tables
   - Verify security group rules
   - Check NACL configurations

### Useful Commands

```bash
# Check attachment status
terraform output transit_gateway_vpc_attachment_id

# View all outputs
terraform output

# Check TGW route tables
aws ec2 describe-transit-gateway-route-tables --region your-region
```

## Security Considerations

1. **Network Segmentation**: Use separate route tables for different environments
2. **Security Groups**: Configure appropriate security group rules
3. **NACLs**: Consider Network ACLs for additional security
4. **Monitoring**: Enable VPC Flow Logs for traffic monitoring

## Cost Considerations

- **Attachment Fee**: ~$36/month per VPC attachment
- **Data Processing**: ~$0.02 per GB processed
- **Cross-AZ Traffic**: Standard data transfer charges apply

## Next Steps

After successful deployment:

1. **Configure Route Tables**: Set up routing in the Transit Gateway route tables
2. **Test Connectivity**: Verify connectivity between VPCs
3. **Monitor Traffic**: Enable logging and monitoring
4. **Security Review**: Review and adjust security group rules
