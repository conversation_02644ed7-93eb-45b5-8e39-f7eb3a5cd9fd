# =============================================================================
# IDENTITY MODULE VARIABLES
# =============================================================================
# This file defines all input variables for the identity module

# -----------------------------------------------------------------------------
# BASIC CONFIGURATION
# -----------------------------------------------------------------------------

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# -----------------------------------------------------------------------------
# CSI DRIVER CONFIGURATION
# -----------------------------------------------------------------------------

variable "enable_ebs_csi_driver" {
  description = "Enable EBS CSI driver IAM role creation"
  type        = bool
  default     = true
}

variable "enable_efs" {
  description = "Enable EFS CSI driver IAM role creation"
  type        = bool
  default     = false
}

# -----------------------------------------------------------------------------
# AUTHENTICATION CONFIGURATION
# -----------------------------------------------------------------------------

variable "enable_pod_identity" {
  description = "Whether Pod Identity is enabled (affects trust policy)"
  type        = bool
  default     = true
}

# -----------------------------------------------------------------------------
# ACCESS ROLES CONFIGURATION
# -----------------------------------------------------------------------------

variable "create_developer_user" {
  description = "Create developer IAM user and access"
  type        = bool
  default     = false
}

variable "create_manager_role" {
  description = "Create manager IAM role and access"
  type        = bool
  default     = false
}

# Note: IRSA trust policies will be handled by a separate process
# The OIDC provider is created by the OIDC module after the EKS cluster is created
