# Security Module - Key Pairs and Security Resources
# This module manages SSH key pairs and other security-related resources

# Generate TLS Private Key for Bastion Host SSH Access
resource "tls_private_key" "bastion_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

# Create AWS Key Pair using the generated public key
resource "aws_key_pair" "bastion_key" {
  key_name   = "${var.project_name}-${var.environment}-bastion-key"
  public_key = tls_private_key.bastion_key.public_key_openssh

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-bastion-key"
      Type = "bastion-ssh-key"
    }
  )
}

# Store private key in AWS Systems Manager Parameter Store (encrypted)
resource "aws_ssm_parameter" "bastion_private_key" {
  name        = "/${var.project_name}/${var.environment}/bastion/private-key"
  description = "Private SSH key for bastion host access"
  type        = "SecureString"
  value       = tls_private_key.bastion_key.private_key_pem

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-bastion-private-key"
      Type = "ssh-private-key"
    }
  )
}

# Store public key in AWS Systems Manager Parameter Store
resource "aws_ssm_parameter" "bastion_public_key" {
  name        = "/${var.project_name}/${var.environment}/bastion/public-key"
  description = "Public SSH key for bastion host access"
  type        = "String"
  value       = tls_private_key.bastion_key.public_key_openssh

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-bastion-public-key"
      Type = "ssh-public-key"
    }
  )
}

# Create a local file with the private key for easy access (optional)
resource "local_file" "bastion_private_key" {
  count           = var.create_local_key_file ? 1 : 0
  content         = tls_private_key.bastion_key.private_key_pem
  filename        = "${path.root}/keys/${var.project_name}-${var.environment}-bastion-key.pem"
  file_permission = "0400"
}

# Create a local file with the public key for reference (optional)
resource "local_file" "bastion_public_key" {
  count           = var.create_local_key_file ? 1 : 0
  content         = tls_private_key.bastion_key.public_key_openssh
  filename        = "${path.root}/keys/${var.project_name}-${var.environment}-bastion-key.pub"
  file_permission = "0644"
}
