# ECR Registry Separation Guide

## 🎯 Overview

This guide explains how ECR (Elastic Container Registry) repositories are separated between development and production environments in our Terraform EKS infrastructure.

## 🏗️ Architecture

### Environment-Based Repository Naming

Our ECR repositories are automatically separated by environment using the following naming convention:

```
{project_name}-{environment}-{repository_name}
```

**Examples:**
- Development: `fresh-eks-dev-app-frontend`, `fresh-eks-dev-app-backend`
- Production: `fresh-eks-prod-app-frontend`, `fresh-eks-prod-app-backend`

### Repository Structure

```
ECR Repositories
├── Development Environment
│   ├── fresh-eks-dev-app-frontend
│   ├── fresh-eks-dev-app-backend
│   └── fresh-eks-dev-nginx
└── Production Environment
    ├── fresh-eks-prod-app-frontend
    ├── fresh-eks-prod-app-backend
    ├── fresh-eks-prod-app-api
    ├── fresh-eks-prod-nginx
    ├── fresh-eks-prod-redis
    ├── fresh-eks-prod-monitoring
    └── fresh-eks-prod-logging
```

## 🔧 Configuration

### Development Environment (`dev.tfvars`)

```hcl
# ECR REGISTRY CONFIGURATION - DEVELOPMENT ENVIRONMENT
enable_ecr_registry = true
ecr_repository_names = [
  "app-frontend",    # Frontend application
  "app-backend",     # Backend API
  "nginx"           # Web server
]
ecr_image_tag_mutability = "MUTABLE"      # Allow tag overwriting in dev
ecr_max_image_count = 5                   # Keep fewer images in dev
ecr_lifecycle_tag_prefixes = ["dev", "feature", "test"]
```

### Production Environment (`prod.tfvars`)

```hcl
# ECR REGISTRY CONFIGURATION - PRODUCTION ENVIRONMENT
enable_ecr_registry = true
ecr_repository_names = [
  "app-frontend",    # Frontend application
  "app-backend",     # Backend API
  "app-api",         # Additional API services
  "nginx",           # Web server/reverse proxy
  "redis",           # Redis cache
  "monitoring",      # Monitoring tools
  "logging"          # Logging services
]
ecr_image_tag_mutability = "IMMUTABLE"    # Prevent tag overwriting in production
ecr_max_image_count = 50                  # Keep more images in production
ecr_encryption_type = "KMS"               # Enhanced security for production
ecr_lifecycle_tag_prefixes = ["v", "release", "prod", "stable"]
```

## 🔐 Security Differences

### Development Environment
- **Tag Mutability**: `MUTABLE` - Allows overwriting tags for faster development
- **Encryption**: `AES256` - Standard encryption
- **Image Retention**: Lower limits for cost optimization
- **Scanning**: Enabled for security awareness

### Production Environment
- **Tag Mutability**: `IMMUTABLE` - Prevents accidental tag overwriting
- **Encryption**: `KMS` - Enhanced encryption with AWS KMS
- **Image Retention**: Higher limits for rollback capabilities
- **Scanning**: Enabled with stricter policies

## 📋 Usage Examples

### Building and Pushing Images

#### Development
```bash
# Build image
docker build -t fresh-eks-dev-app-frontend:latest .

# Tag for ECR
docker tag fresh-eks-dev-app-frontend:latest \
  123456789012.dkr.ecr.us-west-2.amazonaws.com/fresh-eks-dev-app-frontend:latest

# Push to ECR
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/fresh-eks-dev-app-frontend:latest
```

#### Production
```bash
# Build image
docker build -t fresh-eks-prod-app-frontend:v1.2.3 .

# Tag for ECR
docker tag fresh-eks-prod-app-frontend:v1.2.3 \
  123456789012.dkr.ecr.us-west-2.amazonaws.com/fresh-eks-prod-app-frontend:v1.2.3

# Push to ECR
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/fresh-eks-prod-app-frontend:v1.2.3
```

### Kubernetes Deployment

#### Development
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-frontend
  namespace: development
spec:
  template:
    spec:
      containers:
      - name: frontend
        image: 123456789012.dkr.ecr.us-west-2.amazonaws.com/fresh-eks-dev-app-frontend:latest
```

#### Production
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-frontend
  namespace: production
spec:
  template:
    spec:
      containers:
      - name: frontend
        image: 123456789012.dkr.ecr.us-west-2.amazonaws.com/fresh-eks-prod-app-frontend:v1.2.3
```

## 🚀 Deployment Commands

### Deploy Development ECR
```bash
terraform apply -var-file="dev.tfvars" -target=module.ecr_registry
```

### Deploy Production ECR
```bash
terraform apply -var-file="prod.tfvars" -target=module.ecr_registry
```

## 📊 Benefits

### 🔒 **Security Isolation**
- Complete separation between dev and prod images
- Different security policies per environment
- Immutable tags in production prevent accidents

### 💰 **Cost Optimization**
- Different retention policies per environment
- Aggressive cleanup in development
- Longer retention in production for rollbacks

### 🔄 **Development Workflow**
- Mutable tags in dev for rapid iteration
- Immutable tags in prod for stability
- Environment-specific lifecycle policies

### 📈 **Operational Excellence**
- Clear naming conventions
- Environment-specific configurations
- Automated lifecycle management

## 🛠️ Management Commands

### List All Repositories
```bash
aws ecr describe-repositories --query 'repositories[].repositoryName' --output table
```

### Get Repository URLs
```bash
# Development
terraform output ecr_repository_urls

# Production  
terraform output ecr_repository_urls
```

### Docker Login
```bash
# Get login command from Terraform output
terraform output ecr_docker_login_command
```

## 📝 Best Practices

1. **Use Semantic Versioning** in production (v1.2.3)
2. **Use Descriptive Tags** in development (feature-auth, dev-latest)
3. **Regular Cleanup** of unused images
4. **Monitor Repository Sizes** for cost control
5. **Enable Image Scanning** for security
6. **Use Lifecycle Policies** for automated management

## 🔍 Monitoring

### Repository Metrics
- Image count per repository
- Repository size
- Push/pull frequency
- Vulnerability scan results

### Cost Monitoring
- Storage costs per environment
- Data transfer costs
- Lifecycle policy effectiveness

This separation strategy ensures secure, cost-effective, and operationally excellent container registry management across environments.
