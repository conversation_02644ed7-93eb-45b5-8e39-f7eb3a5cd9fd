resource "aws_security_group" "general_use" {
  name   = "${var.project_name}-${var.environment}-general-sg"
  vpc_id = aws_vpc.main.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # ingress {
  #   from_port   = 22
  #   to_port     = 22
  #   protocol    = "tcp"
  #   cidr_blocks = [var.vpc_cidr]
  # }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-general-sg"
      Environment = var.environment
      Project     = var.project_name
    }
  )
}



resource "aws_security_group" "bastion_sg" {
  name        = "${var.project_name}-${var.environment}-bastion-sg"
  description = "Allow SSH and HTTPS inbound traffic and all outbound traffic"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Better to be my own ip
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-bastion-server-sg"
      Environment = var.environment
      Project     = var.project_name
    }
  )
}

resource "aws_security_group" "control_plane_sg" {
  name        = "${var.project_name}-${var.environment}-control-plane-sg"
  description = "Allow HTTPS inbound traffic and all outbound traffic"

  vpc_id = aws_vpc.main.id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-control-plane-sg"
      Environment = var.environment
      Project     = var.project_name
    }
  )
}


resource "aws_security_group" "eks-worker-sg" {
  name        = "${var.project_name}-${var.environment}-eks-worker-sg"
  description = "Allow SSH and HTTPS inbound traffic and all outbound traffic, also allow node-to-node communications"
  vpc_id      = aws_vpc.main.id

  # Allow SSH from bastion
  ingress {
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion_sg.id]
  }


  # Allow pods to communicate with the cluster API Server
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }


  tags = merge(
    var.common_tags,
    {
      Name                                                = "${var.project_name}-${var.environment}-worker-sg"
      Environment                                         = var.environment
      Project                                             = var.project_name
      "kubernetes.io/cluster/${var.project_name}-cluster" = "owned"
    }
  )
} 