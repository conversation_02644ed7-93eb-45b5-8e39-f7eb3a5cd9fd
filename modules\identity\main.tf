# =============================================================================
# IDENTITY MODULE - IAM ROLES AND POLICIES
# =============================================================================
# This module manages all IAM roles, policies, and user access for the EKS infrastructure
# It centralizes identity management for better security and maintainability

# -----------------------------------------------------------------------------
# DATA SOURCES
# -----------------------------------------------------------------------------
# Get current AWS account ID and partition for constructing ARNs
data "aws_caller_identity" "current" {}
data "aws_partition" "current" {}

# =============================================================================
# EKS CLUSTER SERVICE ROLE
# =============================================================================
# This role is assumed by the EKS service to manage the cluster control plane
# It allows EKS to create and manage AWS resources on behalf of the cluster

resource "aws_iam_role" "cluster" {
  name = "${var.cluster_name}-cluster-role"

  # Trust policy: Allows EKS service to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          # Only the EKS service can assume this role
          Service = "eks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-cluster-role"
      Type = "eks-cluster-role"
    }
  )
}

# EKS Cluster Policy Attachment
# Attaches the AWS managed policy that provides necessary permissions for EKS
resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSClusterPolicy" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.cluster.name
}

# =============================================================================
# EKS NODE GROUP SERVICE ROLE
# =============================================================================
# This role is assumed by EC2 instances in the node groups
# It provides permissions for worker nodes to join the cluster and function properly

resource "aws_iam_role" "node_group" {
  name = "${var.cluster_name}-node-group-role"

  # Trust policy: Allows EC2 instances to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          # Only EC2 instances can assume this role
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-node-group-role"
      Type = "eks-node-group-role"
    }
  )
}

# Node Group Policy Attachments
# These AWS managed policies provide the necessary permissions for worker nodes

# 1. AmazonEKSWorkerNodePolicy
# Allows worker nodes to connect to EKS cluster and register themselves
resource "aws_iam_role_policy_attachment" "node_group_AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.node_group.name
}

# 2. AmazonEKS_CNI_Policy
# Allows worker nodes to manage ENIs for pod networking
resource "aws_iam_role_policy_attachment" "node_group_AmazonEKS_CNI_Policy" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.node_group.name
}

# 3. AmazonEC2ContainerRegistryReadOnly
# Allows worker nodes to pull container images from ECR
resource "aws_iam_role_policy_attachment" "node_group_AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.node_group.name
}

# =============================================================================
# EBS CSI DRIVER IAM ROLE
# =============================================================================
# This role is used by the EBS CSI driver pods to authenticate with AWS

resource "aws_iam_role" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0
  name  = "${var.cluster_name}-ebs-csi-driver"

  # Trust policy for Pod Identity
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          # Pod Identity service principal
          Service = "pods.eks.amazonaws.com"
        }
        Action = [
          "sts:AssumeRole",
          "sts:TagSession" # Required for Pod Identity
        ]
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-csi-role"
      Type = "csi-driver-role"
    }
  )
}

# EBS CSI Driver Policy Attachment
resource "aws_iam_role_policy_attachment" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0

  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
  role       = aws_iam_role.ebs_csi_driver[0].name
}

# =============================================================================
# EFS CSI DRIVER IAM ROLE
# =============================================================================
# This role is used by the EFS CSI driver pods to authenticate with AWS

resource "aws_iam_role" "efs_csi_driver" {
  count = var.enable_efs ? 1 : 0
  name  = "${var.cluster_name}-efs-csi-driver"

  # Trust policy for Pod Identity
  # Note: IRSA trust policies will be handled separately to avoid circular dependencies
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          # Pod Identity service principal
          Service = "pods.eks.amazonaws.com"
        }
        Action = [
          "sts:AssumeRole",
          "sts:TagSession" # Required for Pod Identity
        ]
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-efs-csi-role"
      Type = "csi-driver-role"
    }
  )
}

# EFS CSI Driver Policy Attachment
resource "aws_iam_role_policy_attachment" "efs_csi_driver" {
  count = var.enable_efs ? 1 : 0

  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/service-role/AmazonEFSCSIDriverPolicy"
  role       = aws_iam_role.efs_csi_driver[0].name
}

# =============================================================================
# DEVELOPER ACCESS CONFIGURATION
# =============================================================================
# Creates a developer IAM user with limited EKS permissions

resource "aws_iam_user" "developer" {
  count = var.create_developer_user ? 1 : 0
  name  = "${var.cluster_name}-developer"

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer"
      Type = "developer-user"
    }
  )
}

# Developer Access Key (for programmatic access)
resource "aws_iam_access_key" "developer" {
  count = var.create_developer_user ? 1 : 0
  user  = aws_iam_user.developer[0].name
}

# Developer Policy for EKS Access
resource "aws_iam_policy" "developer_eks_access" {
  count = var.create_developer_user ? 1 : 0
  name  = "${var.cluster_name}-developer-eks-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "eks:DescribeCluster",
          "eks:ListClusters"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer-policy"
      Type = "developer-policy"
    }
  )
}

# Attach Developer Policy to User
resource "aws_iam_user_policy_attachment" "developer_eks_access" {
  count      = var.create_developer_user ? 1 : 0
  user       = aws_iam_user.developer[0].name
  policy_arn = aws_iam_policy.developer_eks_access[0].arn
}

# =============================================================================
# MANAGER ACCESS CONFIGURATION
# =============================================================================
# Creates a manager IAM user and admin role for elevated permissions

# Manager IAM User
resource "aws_iam_user" "manager" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-manager"

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-manager"
      Type = "manager-user"
    }
  )
}

# Manager Access Key (for programmatic access)
resource "aws_iam_access_key" "manager" {
  count = var.create_manager_role ? 1 : 0
  user  = aws_iam_user.manager[0].name
}

# EKS Admin Role for Managers
resource "aws_iam_role" "eks_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-eks-admin"

  # Trust policy: Allows users in this AWS account to assume the role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "sts:AssumeRole"
        Principal = {
          # Allow any user in this AWS account to assume the role
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-admin-role"
      Type = "admin-role"
    }
  )
}

# EKS Admin Policy
resource "aws_iam_policy" "eks_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-eks-admin-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "eks:*"
        ]
        Resource = "*"
      },
      {
        Effect   = "Allow"
        Action   = "iam:PassRole"
        Resource = "*"
        Condition = {
          StringEquals = {
            "iam:PassedToService" = "eks.amazonaws.com"
          }
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-admin-policy"
      Type = "admin-policy"
    }
  )
}

# Attach Admin Policy to Admin Role
resource "aws_iam_role_policy_attachment" "eks_admin" {
  count      = var.create_manager_role ? 1 : 0
  role       = aws_iam_role.eks_admin[0].name
  policy_arn = aws_iam_policy.eks_admin[0].arn
}

# Manager Policy to Assume Admin Role
resource "aws_iam_policy" "manager_assume_role" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-manager-assume-role-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "sts:AssumeRole"
        Resource = aws_iam_role.eks_admin[0].arn
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-manager-assume-policy"
      Type = "assume-role-policy"
    }
  )
}

# Attach Assume Role Policy to Manager User
resource "aws_iam_user_policy_attachment" "manager_assume_role" {
  count      = var.create_manager_role ? 1 : 0
  user       = aws_iam_user.manager[0].name
  policy_arn = aws_iam_policy.manager_assume_role[0].arn
}
