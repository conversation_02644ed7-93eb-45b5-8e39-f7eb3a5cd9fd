# Security Module

This module manages security-related resources including SSH key pairs for bastion host access.

## Features

- **SSH Key Generation**: Automatically generates RSA SSH key pairs
- **AWS Key Pair Management**: Creates and manages EC2 key pairs
- **Secure Storage**: Stores private keys in AWS Systems Manager Parameter Store (encrypted)
- **Local Key Files**: Optionally creates local key files for easy access
- **Flexible Configuration**: Supports different key algorithms and sizes

## Resources Created

### Key Pair Resources
- `tls_private_key.bastion_key` - Generates the SSH key pair
- `aws_key_pair.bastion_key` - Creates the EC2 key pair in AWS
- `aws_ssm_parameter.bastion_private_key` - Stores private key in SSM (encrypted)
- `aws_ssm_parameter.bastion_public_key` - Stores public key in SSM
- `local_file.bastion_private_key` - Optional local private key file
- `local_file.bastion_public_key` - Optional local public key file

## Usage

```hcl
module "security" {
  source = "./modules/security"

  project_name           = "eks-infra"
  environment           = "dev"
  create_local_key_file = true
  
  common_tags = {
    Environment = "dev"
    Project     = "eks-infra"
  }
}
```

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| project_name | Project name for resource naming | string | - | yes |
| environment | Environment name | string | - | yes |
| common_tags | Common tags to apply to all resources | map(string) | {} | no |
| create_local_key_file | Whether to create local key files | bool | false | no |
| key_algorithm | Algorithm for SSH key generation | string | "RSA" | no |
| rsa_bits | Number of bits for RSA key | number | 4096 | no |

## Outputs

| Name | Description | Sensitive |
|------|-------------|-----------|
| bastion_key_pair_name | Name of the bastion host key pair | no |
| bastion_key_pair_id | ID of the bastion host key pair | no |
| bastion_key_pair_arn | ARN of the bastion host key pair | no |
| bastion_private_key_pem | Private key in PEM format | yes |
| bastion_public_key_openssh | Public key in OpenSSH format | no |
| bastion_private_key_ssm_parameter_name | SSM parameter name for private key | no |
| bastion_public_key_ssm_parameter_name | SSM parameter name for public key | no |

## Security Considerations

### Private Key Storage
- **SSM Parameter Store**: Private keys are stored encrypted in AWS Systems Manager
- **Local Files**: Only created if explicitly enabled via `create_local_key_file`
- **Terraform State**: Private keys are stored in Terraform state (ensure state is encrypted)

### Access Control
- **SSM Parameters**: Use IAM policies to control access to stored keys
- **Local Files**: Created with restrictive permissions (0400 for private key)
- **Key Rotation**: Keys can be rotated by destroying and recreating the module

## Accessing the Private Key

### From SSM Parameter Store
```bash
# Get private key from SSM
aws ssm get-parameter \
    --name "/eks-infra/dev/bastion/private-key" \
    --with-decryption \
    --query 'Parameter.Value' \
    --output text > ~/.ssh/bastion-key.pem

# Set proper permissions
chmod 400 ~/.ssh/bastion-key.pem
```

### From Local File (if enabled)
```bash
# Key is automatically created in keys/ directory
ls -la keys/
# Use the key directly
ssh -i keys/eks-infra-dev-bastion-key.pem ubuntu@<bastion-ip>
```

## Integration with Compute Module

The security module is designed to work seamlessly with the compute module:

```hcl
# Security module creates the key pair
module "security" {
  source = "./modules/security"
  # ... configuration
}

# Compute module uses the key pair
module "compute" {
  source = "./modules/compute"
  
  key_name = module.security.bastion_key_pair_name
  # ... other configuration
}
```

## Key Rotation

To rotate keys:

1. **Destroy the security module**:
   ```bash
   terraform destroy -target=module.security
   ```

2. **Re-apply to create new keys**:
   ```bash
   terraform apply
   ```

3. **Update any systems using the old keys**

## Troubleshooting

### Permission Issues
- Ensure proper file permissions on local key files (0400)
- Verify IAM permissions for SSM parameter access

### Key Not Found
- Check that the security module is deployed before the compute module
- Verify the key pair name matches between modules

### SSM Access Issues
- Ensure AWS credentials have `ssm:GetParameter` permissions
- Check that the parameter exists in the correct region
