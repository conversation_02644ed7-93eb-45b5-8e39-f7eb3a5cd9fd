# AWS Region
variable "aws_region" {
  description = "AWS region for backend resources"
  type        = string
  default     = "eu-west-1"
}

# Dev Backend S3 Bucket Name
variable "dev_backend_bucket_name" {
  description = "Name of the S3 bucket for dev Terraform state storage"
  type        = string
  default     = "eks-main-terraform-state-ireland-dev"
}

# Prod Backend S3 Bucket Name
variable "prod_backend_bucket_name" {
  description = "Name of the S3 bucket for prod Terraform state storage"
  type        = string
  default     = "eks-main-terraform-state-ireland-prod"
}

# Dev DynamoDB Table Name
variable "dev_dynamodb_table_name" {
  description = "Name of the DynamoDB table for dev Terraform state locking"
  type        = string
  default     = "terraform-state-lock-dev"
}

# Prod DynamoDB Table Name
variable "prod_dynamodb_table_name" {
  description = "Name of the DynamoDB table for prod Terraform state locking"
  type        = string
  default     = "terraform-state-lock-prod"
}

# DynamoDB Billing Mode
variable "billing_mode" {
  description = "Billing mode for DynamoDB tables"
  type        = string
  default     = "PAY_PER_REQUEST"
  validation {
    condition     = contains(["PAY_PER_REQUEST", "PROVISIONED"], var.billing_mode)
    error_message = "Billing mode must be either PAY_PER_REQUEST or PROVISIONED."
  }
} 