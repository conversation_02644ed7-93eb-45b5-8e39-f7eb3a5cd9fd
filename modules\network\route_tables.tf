# Route Tables
resource "aws_route_table" "private_eks_az1" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-private-eks-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private EKS Route Table"
      AZ          = var.availability_zones[0]
    }
  )
}
resource "aws_route_table" "private_eks_az2" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-private-eks-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private EKS Route Table"
      AZ          = var.availability_zones[1]
    }
  )
}
resource "aws_route_table" "private_lb_az1" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-private-lb-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private LB Route Table"
      AZ          = var.availability_zones[0]
    }
  )
}
resource "aws_route_table" "private_lb_az2" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-private-lb-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private LB Route Table"
      AZ          = var.availability_zones[1]
    }
  )
}
resource "aws_route_table" "intra_az1" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-intra-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated) Route Table"
      AZ          = var.availability_zones[0]
    }
  )
}
resource "aws_route_table" "intra_az2" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-intra-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated) Route Table"
      AZ          = var.availability_zones[1]
    }
  )
}
resource "aws_route_table" "tgw_attachment_1a" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-tgw-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "TGW Attachment Route Table"
      AZ          = var.availability_zones[0]
    }
  )
}
resource "aws_route_table" "tgw_attachment_1b" {
  vpc_id = aws_vpc.main.id
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-tgw-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "TGW Attachment Route Table"
      AZ          = var.availability_zones[1]
    }
  )
} 