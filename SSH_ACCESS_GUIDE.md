# Terraform EKS Infrastructure Setup Guide

> **Note:** This project uses a comprehensive modular architecture with separate `network`, `compute`, `eks`, and `helm` modules. This guide provides complete setup instructions for both development and production environments.

This guide explains how to properly set up the complete Terraform EKS infrastructure stack, including VPC networking, bastion hosts, EKS clusters, and Helm applications using the bootstrap approach to avoid circular dependencies.

## 🏗️ Architecture Overview

The infrastructure includes:
- **Network Module**: VPC, subnets, Transit Gateway, security groups
- **Compute Module**: Bastion hosts for secure access
- **EKS Module**: Managed Kubernetes cluster with node groups
- **Helm Module**: Essential Kubernetes applications (metrics-server, cluster-autoscaler, AWS Load Balancer Controller)

## 🎛️ Simple Module Control

The infrastructure provides simple control over which modules to deploy:

- `enable_network_module`: Controls VPC, subnets, and security groups
- `enable_compute_module`: Controls bastion hosts
- `enable_eks_module`: Controls EKS cluster
- `enable_helm_module`: Controls Helm applications

**Staged Deployment Approach:**
1. **Stage 1**: Deploy network + compute modules first for testing
2. **Stage 2**: Enable EKS module once network is validated
3. **Stage 3**: Enable Helm module to deploy Kubernetes applications

## 🚨 Critical Architecture Change

The project has been restructured to follow best practices by separating the backend infrastructure (S3 bucket and DynamoDB table) from the main EKS infrastructure. This eliminates the dangerous circular dependency that existed in the previous version.

## 📁 Project Structure

```
Terraform-EKS-/
├── bootstrap/                    # Backend infrastructure (runs once)
│   ├── main.tf                  # S3 bucket and DynamoDB table
│   ├── variables.tf             # Bootstrap variables
│   ├── outputs.tf               # Backend configuration outputs
│   ├── terraform.tfvars         # Bootstrap configuration
│   └── README.md                # Bootstrap documentation
├── config/                      # Backend configuration files
│   ├── dev.backend.hcl          # Development backend config
│   └── prod.backend.hcl         # Production backend config
├── modules/                     # Terraform modules
│   ├── network/                 # VPC, subnets, TGW, security groups
│   ├── compute/                 # Bastion hosts and EC2 instances
│   ├── eks/                     # EKS cluster and node groups
│   └── helm/                    # Kubernetes applications via Helm
├── *.tf                         # Root module configuration
├── dev.tfvars                   # Development environment variables
├── prod.tfvars                  # Production environment variables
├── README.md                    # Main project documentation
├── SETUP_GUIDE.md              # This setup guide
├── SUBNET_ARCHITECTURE.md      # Network architecture details
└── TRANSIT_GATEWAY_SETUP.md    # Transit Gateway setup guide
```

## 🚀 Setup Process

### Prerequisites

Before starting, ensure you have:
- **AWS CLI** configured with appropriate permissions
- **Terraform** >= 1.0 installed
- **kubectl** installed for cluster management
- **Helm** >= 3.0 installed for application deployment
- **EC2 Key Pair** created in your target AWS region for bastion host access

### Step 1: Configure Environment Variables

Before deployment, you must customize the `.tfvars` files for your environment:

#### 1.1 Update Transit Gateway ID

Both `dev.tfvars` and `prod.tfvars` contain a placeholder Transit Gateway ID:

```bash
# Edit dev.tfvars
existing_transit_gateway_id = "tgw-00911ebb3cf2ab6a9"  # Replace with your actual TGW ID

# Edit prod.tfvars
existing_transit_gateway_id = "tgw-00911ebb3cf2ab6a9"  # Replace with your actual TGW ID
```

#### 1.2 Configure SSH Key Generation

SSH key pairs are automatically generated by the security module. Configure key generation options:

```bash
# In dev.tfvars - Enable local key files for development
create_local_key_files = true
bastion_key_algorithm  = "RSA"
bastion_rsa_bits      = 4096

# In prod.tfvars - Use SSM Parameter Store only for production
create_local_key_files = false
bastion_key_algorithm  = "RSA"
bastion_rsa_bits      = 4096
```

#### 1.3 Review Network Configuration

Verify that the CIDR blocks don't conflict with your existing networks:
- VPC CIDR: `10.230.0.0/16`
- Subnet ranges are pre-configured for optimal separation

### Step 2: Bootstrap the Backend Infrastructure

Create the S3 bucket and DynamoDB table that will store Terraform state:

```bash
# Navigate to the bootstrap directory
cd bootstrap

# Configure the bootstrap (edit terraform.tfvars if needed)
# The default configuration creates:
# - S3 bucket: eks-main-terraform-state-ireland-unique
# - DynamoDB table: terraform-state-lock

# Initialize and apply the bootstrap
terraform init
terraform plan
terraform apply
```

**Important**: The bootstrap project uses local state to avoid circular dependencies.

### Step 3: Verify Bootstrap Outputs

After successful bootstrap deployment, note the outputs:

```bash
terraform output
```

The key outputs are:
- `terraform_state_bucket_id`: S3 bucket ID
- `dynamodb_table_name`: DynamoDB table name
- `backend_config_hcl`: Backend configuration in HCL format

### Step 4: Configure Main Project Backend

The main project is already configured to use the bootstrap-created backend. The configuration in `provider.tf` is:

```hcl
backend "s3" {
  bucket       = "eks-main-terraform-state-ireland-unique"
  key          = "terraform.tfstate"
  dynamodb_table = "terraform-state-lock"
  region       = "eu-west-1"
  encrypt      = true
  use_lockfile = true
}
```

### Step 5: Deploy Main Infrastructure

#### 5.1 Initialize Terraform

```bash
# Return to the main project directory
cd ..

# Initialize with the backend
terraform init
```

#### 5.2 Deploy Development Environment

```bash
# Create and select development workspace (optional but recommended)
terraform workspace new dev
terraform workspace select dev

# Plan the deployment
terraform plan -var-file=dev.tfvars

# Apply the configuration
terraform apply -var-file=dev.tfvars
```

#### 5.3 Deploy Production Environment

```bash
# Create and select production workspace
terraform workspace new prod
terraform workspace select prod

# Plan the deployment
terraform plan -var-file=prod.tfvars

# Apply the configuration
terraform apply -var-file=prod.tfvars
```

## 🎯 Staged Deployment Strategy

### Simple 3-Stage Deployment

**Stage 1: Network + Compute (Test Infrastructure)**
```hcl
enable_network_module = true
enable_compute_module = true
enable_eks_module     = false
enable_helm_module    = false
```

**Stage 2: Add EKS Cluster**
```hcl
enable_network_module = true
enable_compute_module = true
enable_eks_module     = true
enable_helm_module    = false
```

**Stage 3: Add Helm Applications**
```hcl
enable_network_module = true
enable_compute_module = true
enable_eks_module     = true
enable_helm_module    = true
```

### Deployment Commands

1. **Deploy Stage 1 (Network + Compute):**
   ```bash
# Edit your .tfvars file for Stage 1 settings
   terraform plan -var-file=dev.tfvars
   terraform apply -var-file=dev.tfvars
```

2. **Deploy Stage 2 (Add EKS):**
   ```bash
# Edit your .tfvars file to enable EKS
   terraform plan -var-file=dev.tfvars
   terraform apply -var-file=dev.tfvars
```

3. **Deploy Stage 3 (Add Helm):**
   ```bash
# Edit your .tfvars file to enable Helm
   terraform plan -var-file=dev.tfvars
   terraform apply -var-file=dev.tfvars
```

## 🔄 Environment Management

### Development Environment Configuration

The `dev.tfvars` file is optimized for development with cost-effective settings:

```hcl
# Key development settings
aws_region                       = "eu-west-1"
project_name                     = "eks-main-dev"
environment                      = "dev"

# Cost-optimized EKS configuration
eks_cluster_version              = "1.28"
eks_endpoint_public_access       = true          # Easier kubectl access
eks_cluster_log_retention_days   = 7             # Short retention for cost savings

# Single node group with smaller instances
eks_node_groups = {
  general = {
    instance_types = ["t3.medium"]
    min_size       = 1
    max_size       = 3
    desired_size   = 2
    capacity_type  = "ON_DEMAND"
  }
}

# Single bastion host
bastion_instance_type  = "t3.micro"
bastion_instance_count = 1
```

**Deploy Development:**
```bash
terraform workspace select dev
terraform plan -var-file=dev.tfvars
terraform apply -var-file=dev.tfvars
```

### Production Environment Configuration

The `prod.tfvars` file is optimized for production with security and reliability:

```hcl
# Key production settings
aws_region                       = "us-west-1"
project_name                     = "eks-main-prod"
environment                      = "production"

# Production-hardened EKS configuration
eks_cluster_version              = "1.28"
eks_endpoint_public_access       = false         # Private API for security
eks_cluster_log_retention_days   = 30            # Extended retention for compliance

# Multi-node group setup with high availability
eks_node_groups = {
  general = {
    instance_types = ["m5.large", "m5.xlarge"]
    min_size       = 2
    max_size       = 10
    desired_size   = 3
    capacity_type  = "ON_DEMAND"
  }
  spot = {
    instance_types = ["m5.large", "m5.xlarge", "m4.large", "m4.xlarge"]
    min_size       = 0
    max_size       = 5
    desired_size   = 1
    capacity_type  = "SPOT"
    taints = [
      {
        key    = "spot-instance"
        value  = "true"
        effect = "NO_SCHEDULE"
      }
    ]
  }
}

# High availability bastion hosts
bastion_instance_type  = "t3.small"
bastion_instance_count = 2
```

**Deploy Production:**
```bash
terraform workspace select prod
terraform plan -var-file=prod.tfvars
terraform apply -var-file=prod.tfvars
```

## 🔧 Post-Deployment Configuration

### Step 6: Configure kubectl Access

After successful EKS deployment, configure kubectl to access your cluster:

```bash
# For development environment
aws eks update-kubeconfig --region eu-west-1 --name eks-main-dev-eks

# For production environment
aws eks update-kubeconfig --region us-west-1 --name eks-main-prod-eks

# Verify cluster access
kubectl get nodes
kubectl get pods -A
```

### Step 7: Verify Helm Applications

Check that all Helm applications are deployed successfully:

```bash
# List all Helm releases
helm list -A

# Check metrics server
kubectl get deployment metrics-server -n kube-system

# Check cluster autoscaler
kubectl get deployment cluster-autoscaler -n kube-system

# Check AWS Load Balancer Controller
kubectl get deployment aws-load-balancer-controller -n kube-system
```

### Step 8: Configure EKS Access (Manual Steps Required)

**Important**: The EKS module uses the modern EKS Access Entries API, but some manual configuration may be required:

#### 8.1 Grant Developer Access

```bash
# Create developer access entry (replace with actual user ARN)
aws eks create-access-entry \
  --cluster-name eks-main-dev-eks \
  --principal-arn arn:aws:iam::ACCOUNT-ID:user/developer-username \
  --type STANDARD

# Associate developer policy
aws eks associate-access-policy \
  --cluster-name eks-main-dev-eks \
  --principal-arn arn:aws:iam::ACCOUNT-ID:user/developer-username \
  --policy-arn arn:aws:eks:policy/AmazonEKSViewPolicy \
  --access-scope type=cluster
```

#### 8.2 Grant Manager Access

```bash
# Create manager access entry (replace with actual user ARN)
aws eks create-access-entry \
  --cluster-name eks-main-dev-eks \
  --principal-arn arn:aws:iam::ACCOUNT-ID:user/manager-username \
  --type STANDARD

# Associate manager policy
aws eks associate-access-policy \
  --cluster-name eks-main-dev-eks \
  --principal-arn arn:aws:iam::ACCOUNT-ID:user/manager-username \
  --policy-arn arn:aws:eks:policy/AmazonEKSClusterAdminPolicy \
  --access-scope type=cluster
```

### Step 9: Test Application Deployment

Deploy a test application to verify the cluster is working:

```bash
# Create a test namespace
kubectl create namespace test-app

# Deploy a simple nginx application
kubectl create deployment nginx --image=nginx -n test-app
kubectl expose deployment nginx --port=80 --type=ClusterIP -n test-app

# Verify deployment
kubectl get pods -n test-app
kubectl get services -n test-app
```

## 🛡️ Security Benefits

The architecture provides several security improvements:

1. **No Circular Dependencies**: Backend infrastructure is managed separately
2. **State Protection**: S3 bucket has `prevent_destroy = true`
3. **Encryption**: Both S3 and DynamoDB use encryption at rest
4. **Access Control**: Public access is blocked on S3 bucket
5. **Versioning**: S3 bucket has versioning enabled for state history
6. **Private Infrastructure**: No public IPs or public subnets by default
7. **Modern EKS Access**: Uses EKS Access Entries API instead of aws-auth ConfigMap
8. **IAM Integration**: Proper IAM roles for all components with least privilege

## 🔧 Troubleshooting

### Bootstrap Issues

**Error: Bucket already exists**
- The S3 bucket name must be globally unique
- Update `bootstrap/terraform.tfvars` with a unique bucket name

**Error: Insufficient permissions**
- Ensure AWS credentials have permissions for S3 and DynamoDB
- Required permissions: `s3:CreateBucket`, `dynamodb:CreateTable`

### Configuration Issues

**Error: Transit Gateway not found**
- Verify the `existing_transit_gateway_id` in your `.tfvars` file
- Ensure the Transit Gateway exists in the specified region
- Check that your AWS credentials have access to the Transit Gateway

**Error: Key pair not found**
- Create an EC2 key pair in your target region
- Update `bastion_key_name` in your `.tfvars` file
- Ensure the key pair name matches exactly

**Error: Availability Zone not available**
- Check that the specified availability zones exist in your region
- Update `availability_zones` in your `.tfvars` file with valid AZs
- Use `aws ec2 describe-availability-zones --region <region>` to list available AZs

### Deployment Issues

**Error: Backend not found**
- Ensure the bootstrap project was applied successfully
- Verify the bucket and DynamoDB table exist
- Check the backend configuration in `provider.tf`

**Error: Insufficient IAM permissions**
- Ensure your AWS credentials have the following permissions:
  - VPC and subnet management
  - EKS cluster and node group management
  - IAM role and policy management
  - EC2 instance and security group management

**Error: EKS cluster creation timeout**
- EKS cluster creation can take 10-15 minutes
- Check AWS CloudTrail for detailed error messages
- Verify subnet configurations and routing

**Error: Node group creation failed**
- Check that subnets have sufficient IP addresses
- Verify that the specified instance types are available in your AZs
- Ensure the EKS cluster is in ACTIVE state before creating node groups

### Post-Deployment Issues

**Error: kubectl access denied**
- Ensure you've run `aws eks update-kubeconfig`
- Verify your AWS credentials have EKS access
- Check that the cluster endpoint is accessible (private vs public)

**Error: Helm applications not deploying**
- Verify the EKS cluster is fully ready
- Check that node groups have sufficient capacity
- Review Helm release status: `helm list -A`
- Check pod logs: `kubectl logs -n kube-system deployment/metrics-server`

**Error: Load balancer not working**
- Verify AWS Load Balancer Controller is running
- Check that subnets are properly tagged for load balancer discovery
- Ensure security groups allow the required traffic

### Network Connectivity Issues

**Error: Cannot access bastion host**
- Bastion hosts are in private subnets with no public IPs
- Access must be through Transit Gateway from your datacenter
- Verify Transit Gateway routing and security group rules

**Error: Pods cannot reach internet**
- Check NAT Gateway configuration (if required)
- Verify route tables for private subnets
- Ensure security groups allow outbound traffic

### Monitoring and Debugging

**Check cluster status:**
```bash
kubectl get nodes
kubectl get pods -A
kubectl describe node <node-name>
```

**Check Helm releases:**
```bash
helm list -A
helm status <release-name> -n <namespace>
```

**Check AWS resources:**
```bash
aws eks describe-cluster --name <cluster-name>
aws ec2 describe-instances --filters "Name=tag:Project,Values=<project-name>"
aws elbv2 describe-load-balancers
```

**Check logs:**
```bash
# EKS control plane logs (CloudWatch)
aws logs describe-log-groups --log-group-name-prefix "/aws/eks"

# Pod logs
kubectl logs -n kube-system deployment/cluster-autoscaler
kubectl logs -n kube-system deployment/aws-load-balancer-controller
```

## 📋 Important Notes and Best Practices

### Environment-Specific Considerations

#### Development Environment
- **Public API Access**: Enabled for easier kubectl access during development
- **Cost Optimization**: Uses smaller instance types and shorter log retention
- **Single AZ**: Can be deployed in single AZ for cost savings (update `.tfvars` if needed)
- **Relaxed Security**: Some security restrictions are relaxed for development workflow

#### Production Environment
- **Private API Access**: Only private API access for enhanced security
- **High Availability**: Multi-AZ deployment with multiple node groups
- **Spot Instances**: Includes spot instance node group for cost optimization
- **Extended Logging**: 30-day log retention for compliance and auditing

### Security Best Practices

1. **Access Management**
   - Use EKS Access Entries API instead of aws-auth ConfigMap
   - Implement least privilege access for all users and services
   - Regularly audit and rotate access credentials

2. **Network Security**
   - All infrastructure is private by default (no public IPs)
   - Access through Transit Gateway from datacenter
   - Proper security group rules for each component

3. **Data Protection**
   - EBS volumes are encrypted by default
   - S3 state bucket uses encryption at rest
   - CloudWatch logs are encrypted

### Operational Best Practices

1. **State Management**
   - Always use remote state with locking
   - Use separate workspaces for different environments
   - Regularly backup Terraform state

2. **Monitoring and Alerting**
   - Enable CloudWatch monitoring for all components
   - Set up alerts for cluster health and resource utilization
   - Monitor costs and set up billing alerts

3. **Updates and Maintenance**
   - Regularly update Kubernetes versions
   - Keep Helm charts and applications updated
   - Plan maintenance windows for updates

### Cost Optimization

1. **Instance Selection**
   - Use appropriate instance types for workloads
   - Leverage spot instances for non-critical workloads
   - Implement cluster autoscaling for dynamic scaling

2. **Resource Management**
   - Set resource requests and limits on pods
   - Use horizontal pod autoscaling (HPA)
   - Monitor and optimize resource utilization

3. **Storage Optimization**
   - Use appropriate EBS volume types
   - Implement lifecycle policies for logs
   - Clean up unused resources regularly

## 🚀 Next Steps

After successful deployment:

1. **Configure Monitoring**: Set up Prometheus, Grafana, or CloudWatch monitoring
2. **Implement CI/CD**: Set up deployment pipelines for your applications
3. **Security Hardening**: Implement additional security measures like Pod Security Standards
4. **Backup Strategy**: Implement backup solutions for persistent data
5. **Disaster Recovery**: Plan and test disaster recovery procedures

## 📚 Additional Resources

- [EKS Module Documentation](modules/eks/README.md)
- [Helm Module Documentation](modules/helm/README.md)
- [Network Architecture Guide](SUBNET_ARCHITECTURE.md)
- [Transit Gateway Setup Guide](TRANSIT_GATEWAY_SETUP.md)
- [AWS EKS Best Practices Guide](https://aws.github.io/aws-eks-best-practices/)

## 🆘 Support

If you encounter issues not covered in this guide:

1. Check the module-specific README files for detailed documentation
2. Review AWS CloudTrail logs for detailed error information
3. Consult the official AWS EKS documentation
4. Check Terraform and Helm documentation for provider-specific issues

---

**⚠️ Important**: This infrastructure creates AWS resources that incur costs. Always review the planned changes before applying and clean up resources when no longer needed.