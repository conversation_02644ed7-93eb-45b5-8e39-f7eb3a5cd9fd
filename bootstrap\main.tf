# Bootstrap Terraform Project
# This project creates the S3 backend and DynamoDB tables for dev and prod environments
# IMPORTANT: This project uses LOCAL state to avoid circular dependencies

terraform {
  required_version = ">= 1.2"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.92"
    }
  }
  # NO backend configuration - this project uses local state
}

provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Terraform  = "true"
      Owner      = "DevOps Team"
      CostCenter = "Engineering"
      Purpose    = "Terraform Backend Infrastructure"
    }
  }
}

# --- DEV ENVIRONMENT ---
resource "aws_s3_bucket" "dev_state" {
  bucket        = var.dev_backend_bucket_name
  force_destroy = false

  tags = {
    Name        = "TerraformStateStorageDev"
    Description = "S3 bucket for storing dev Terraform state files"
    Purpose     = "Infrastructure"
    ManagedBy   = "Bootstrap"
    Environment = "dev"
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_versioning" "dev_state_versioning" {
  bucket = aws_s3_bucket.dev_state.id
  versioning_configuration {
    status = "Enabled"
  }
  depends_on = [aws_s3_bucket.dev_state]
}

resource "aws_s3_bucket_server_side_encryption_configuration" "dev_default" {
  bucket = aws_s3_bucket.dev_state.id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
  depends_on = [aws_s3_bucket.dev_state]
}

resource "aws_s3_bucket_public_access_block" "dev_state" {
  bucket                  = aws_s3_bucket.dev_state.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
  depends_on              = [aws_s3_bucket.dev_state]
}

resource "aws_dynamodb_table" "dev_lock" {
  name         = var.dev_dynamodb_table_name
  billing_mode = var.billing_mode
  hash_key     = "LockID"
  attribute {
    name = "LockID"
    type = "S"
  }
  point_in_time_recovery {
    enabled = true
  }
  server_side_encryption {
    enabled = true
  }
  tags = {
    Name        = "TerraformStateLockDev"
    Description = "DynamoDB table for dev Terraform state locking"
    Purpose     = "Infrastructure"
    ManagedBy   = "Bootstrap"
    Environment = "dev"
  }
  lifecycle { prevent_destroy = false }
}

# --- PROD ENVIRONMENT ---
resource "aws_s3_bucket" "prod_state" {
  bucket        = var.prod_backend_bucket_name
  force_destroy = false

  tags = {
    Name        = "TerraformStateStorageProd"
    Description = "S3 bucket for storing prod Terraform state files"
    Purpose     = "Infrastructure"
    ManagedBy   = "Bootstrap"
    Environment = "prod"
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_versioning" "prod_state_versioning" {
  bucket = aws_s3_bucket.prod_state.id
  versioning_configuration {
    status = "Enabled"
  }
  depends_on = [aws_s3_bucket.prod_state]
}

resource "aws_s3_bucket_server_side_encryption_configuration" "prod_default" {
  bucket = aws_s3_bucket.prod_state.id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
  depends_on = [aws_s3_bucket.prod_state]
}

resource "aws_s3_bucket_public_access_block" "prod_state" {
  bucket                  = aws_s3_bucket.prod_state.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
  depends_on              = [aws_s3_bucket.prod_state]
}

resource "aws_dynamodb_table" "prod_lock" {
  name         = var.prod_dynamodb_table_name
  billing_mode = var.billing_mode
  hash_key     = "LockID"
  attribute {
    name = "LockID"
    type = "S"
  }
  point_in_time_recovery {
    enabled = true
  }
  server_side_encryption {
    enabled = true
  }
  tags = {
    Name        = "TerraformStateLockProd"
    Description = "DynamoDB table for prod Terraform state locking"
    Purpose     = "Infrastructure"
    ManagedBy   = "Bootstrap"
    Environment = "prod"
  }
  lifecycle { prevent_destroy = false }
} 