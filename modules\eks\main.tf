# =============================================================================
# EKS MODULE MAIN CONFIGURATION
# =============================================================================
# This file serves as the main entry point for the EKS module
# The actual resources are organized in separate files for better maintainability:
# - cluster.tf: EKS cluster configuration
# - nodes.tf: EKS node groups
# - addons.tf: EKS addons (Pod Identity, EBS CSI Driver, EFS CSI Driver)
# - access-entries.tf: EKS access entries for user access

# Note: IAM roles and OIDC provider are now managed by separate modules:
# - Identity module: Manages all IAM roles and policies
# - OIDC module: Manages OpenID Connect provider for IRSA
