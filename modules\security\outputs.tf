# Security Module Outputs

# Key Pair Outputs
output "bastion_key_pair_name" {
  description = "Name of the bastion host key pair"
  value       = aws_key_pair.bastion_key.key_name
}

output "bastion_key_pair_id" {
  description = "ID of the bastion host key pair"
  value       = aws_key_pair.bastion_key.key_pair_id
}

output "bastion_key_pair_arn" {
  description = "ARN of the bastion host key pair"
  value       = aws_key_pair.bastion_key.arn
}

output "bastion_key_pair_fingerprint" {
  description = "MD5 public key fingerprint"
  value       = aws_key_pair.bastion_key.fingerprint
}

# Private Key Outputs (sensitive)
output "bastion_private_key_pem" {
  description = "Private key in PEM format"
  value       = tls_private_key.bastion_key.private_key_pem
  sensitive   = true
}

output "bastion_private_key_openssh" {
  description = "Private key in OpenSSH format"
  value       = tls_private_key.bastion_key.private_key_openssh
  sensitive   = true
}

# Public Key Outputs
output "bastion_public_key_openssh" {
  description = "Public key in OpenSSH format"
  value       = tls_private_key.bastion_key.public_key_openssh
}

output "bastion_public_key_pem" {
  description = "Public key in PEM format"
  value       = tls_private_key.bastion_key.public_key_pem
}

# SSM Parameter Outputs
output "bastion_private_key_ssm_parameter_name" {
  description = "Name of the SSM parameter storing the private key"
  value       = aws_ssm_parameter.bastion_private_key.name
}

output "bastion_public_key_ssm_parameter_name" {
  description = "Name of the SSM parameter storing the public key"
  value       = aws_ssm_parameter.bastion_public_key.name
}

# Local File Outputs (if created)
output "bastion_private_key_file_path" {
  description = "Path to the local private key file"
  value       = var.create_local_key_file ? local_file.bastion_private_key[0].filename : null
}

output "bastion_public_key_file_path" {
  description = "Path to the local public key file"
  value       = var.create_local_key_file ? local_file.bastion_public_key[0].filename : null
}
