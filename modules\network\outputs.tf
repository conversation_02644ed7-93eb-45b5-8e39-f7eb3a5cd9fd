output "vpc_id" {
  description = "The ID of the VPC"
  value       = aws_vpc.main.id
}

output "private_eks_subnet_ids" {
  description = "IDs of the private EKS subnets"
  value       = [aws_subnet.private_eks_az1.id, aws_subnet.private_eks_az2.id]
}

output "private_lb_subnet_ids" {
  description = "IDs of the private load balancer subnets"
  value       = [aws_subnet.private_lb_az1.id, aws_subnet.private_lb_az2.id]
}

output "intra_subnet_ids" {
  description = "IDs of the intra (isolated) subnets"
  value       = [aws_subnet.intra_az1.id, aws_subnet.intra_az2.id]
}

output "tgw_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = [aws_subnet.tgw_attachment_1a.id, aws_subnet.tgw_attachment_1b.id]
}

output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = aws_security_group.general_use.id
}

output "bastion_sg_id" {
  description = "ID of the bastion security group"
  value       = aws_security_group.bastion_sg.id
} 