# =============================================================================
# ECR REGISTRY MODULE OUTPUTS
# =============================================================================
# This file defines all output values from the ECR registry module

# -----------------------------------------------------------------------------
# REPOSITORY OUTPUTS
# -----------------------------------------------------------------------------

output "repository_urls" {
  description = "Map of repository names to their URLs"
  value = {
    for name, repo in aws_ecr_repository.repositories :
    name => repo.repository_url
  }
}

output "repository_arns" {
  description = "Map of repository names to their ARNs"
  value = {
    for name, repo in aws_ecr_repository.repositories :
    name => repo.arn
  }
}

output "repository_names" {
  description = "Map of original names to full repository names"
  value = {
    for name, repo in aws_ecr_repository.repositories :
    name => repo.name
  }
}

output "registry_id" {
  description = "The registry ID where the repositories were created"
  value       = length(aws_ecr_repository.repositories) > 0 ? values(aws_ecr_repository.repositories)[0].registry_id : null
}

# -----------------------------------------------------------------------------
# INDIVIDUAL REPOSITORY OUTPUTS
# -----------------------------------------------------------------------------

output "repositories" {
  description = "Complete information about all created repositories"
  value = {
    for name, repo in aws_ecr_repository.repositories : name => {
      arn                    = repo.arn
      name                   = repo.name
      registry_id            = repo.registry_id
      repository_url         = repo.repository_url
      image_tag_mutability   = repo.image_tag_mutability
      image_scanning_enabled = repo.image_scanning_configuration[0].scan_on_push
      encryption_type        = repo.encryption_configuration[0].encryption_type
      kms_key                = repo.encryption_configuration[0].kms_key
    }
  }
}

# -----------------------------------------------------------------------------
# DOCKER COMMANDS OUTPUT
# -----------------------------------------------------------------------------

output "docker_login_command" {
  description = "AWS CLI command to authenticate Docker with ECR"
  value       = "aws ecr get-login-password --region ${data.aws_region.current.name} | docker login --username AWS --password-stdin ${data.aws_caller_identity.current.account_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com"
}

output "docker_commands" {
  description = "Docker commands for each repository"
  value = {
    for name, repo in aws_ecr_repository.repositories : name => {
      login = "aws ecr get-login-password --region ${data.aws_region.current.name} | docker login --username AWS --password-stdin ${repo.registry_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com"
      build = "docker build -t ${repo.name} ."
      tag   = "docker tag ${repo.name}:latest ${repo.repository_url}:latest"
      push  = "docker push ${repo.repository_url}:latest"
      pull  = "docker pull ${repo.repository_url}:latest"
    }
  }
}

# -----------------------------------------------------------------------------
# GITOPS INTEGRATION OUTPUTS
# -----------------------------------------------------------------------------

output "gitops_repositories" {
  description = "Repository information formatted for GitOps workflows"
  value = {
    for name, repo in aws_ecr_repository.repositories : name => {
      registry   = "${repo.registry_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com"
      repository = repo.name
      full_url   = repo.repository_url
      region     = data.aws_region.current.name
      account_id = data.aws_caller_identity.current.account_id
    }
  }
}

# -----------------------------------------------------------------------------
# HELM VALUES OUTPUTS
# -----------------------------------------------------------------------------

output "helm_image_values" {
  description = "Image values for Helm charts"
  value = {
    for name, repo in aws_ecr_repository.repositories : name => {
      repository = repo.repository_url
      tag        = "latest"
      pullPolicy = "Always"
    }
  }
}

# -----------------------------------------------------------------------------
# SUMMARY OUTPUTS
# -----------------------------------------------------------------------------

output "summary" {
  description = "Summary of ECR registry configuration"
  value = {
    total_repositories       = length(aws_ecr_repository.repositories)
    registry_id              = length(aws_ecr_repository.repositories) > 0 ? values(aws_ecr_repository.repositories)[0].registry_id : null
    region                   = data.aws_region.current.name
    account_id               = data.aws_caller_identity.current.account_id
    image_scanning_enabled   = var.enable_image_scanning
    lifecycle_policy_enabled = var.enable_lifecycle_policy
    encryption_type          = var.encryption_type
    cross_account_enabled    = var.enable_cross_account_access
    repository_list          = [for name, repo in aws_ecr_repository.repositories : repo.name]
  }
}
