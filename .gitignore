# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore Mac .DS_Store files
.DS_Store

# Ignore Windows Thumbs.db files
Thumbs.db

# SSH Keys directory (contains sensitive private keys)
keys/
*.pem
*.key

# Ignore editor files
*.swp
*.swo
*~

# Ignore IDE files
.vscode/
.idea/
*.iml

# Ignore log files
*.log

# Ignore terraform-examples directory
terraform-examples/

# Ignore terraform.lock.hcl file
.terraform.lock.hcl

tfplan*

dev-state-backup.json
prod-state-backup.json