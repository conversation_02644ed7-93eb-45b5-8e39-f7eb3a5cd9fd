# Network Module

This module creates a comprehensive network infrastructure for EKS deployments with Transit Gateway integration and security groups.

## Overview

The network module provides:
- VPC with DNS support enabled
- Private EKS subnets (2 AZs)
- Private Load Balancer subnets (2 AZs)
- Intra (isolated) subnets (2 AZs)
- Transit Gateway attachment subnets (2 AZs)
- Route tables and associations
- Conditional Transit Gateway routing and VPC attachment
- Security groups for general use, EKS, ALB, bastion, and worker nodes

## File Structure

```
modules/network/
├── main.tf                 # VPC, TGW, and core resources
├── subnets.tf              # All subnet resources
├── route_tables.tf         # Route table resources
├── route_associations.tf   # Route table associations
├── routes.tf               # Route resources (TGW routes)
├── security_groups.tf      # All security group resources
├── variables.tf            # Input variables
├── outputs.tf              # Output values
└── README.md               # This file
```

## Usage

```hcl
module "network" {
  source = "./modules/network"
  vpc_cidr                    = "**********/16"
  availability_zones          = ["eu-west-1a", "eu-west-1b"]
  private_eks_subnet_cidrs    = {
    az1 = "***********/24"
    az2 = "***********/24"
  }
  private_lb_subnet_cidrs     = {
    az1 = "***********/24"
    az2 = "***********/24"
  }
  intra_subnet_cidrs          = {
    az1 = "***********/24"
    az2 = "***********/24"
  }
  tgw_subnet_cidrs            = ["**********/26", "**********/26"]
  map_public_ip_on_launch     = false
  create_transit_gateway_attachment = true
  existing_transit_gateway_id = "tgw-xxxxxxxxx"
  enable_dns_support_attachment = true
  enable_ipv6_support_attachment = false
  appliance_mode_support         = "disable"
  tags                        = {
    Environment = "dev"
    Project     = "eks-infra"
  }
  project_name = "eks-infra"
  environment  = "dev"
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_cidr | CIDR block for the VPC | `string` | n/a | yes |
| availability_zones | List of availability zones | `list(string)` | n/a | yes |
| private_eks_subnet_cidrs | CIDR blocks for private EKS subnets | `map(string)` | n/a | yes |
| private_lb_subnet_cidrs | CIDR blocks for private load balancer subnets | `map(string)` | n/a | yes |
| intra_subnet_cidrs | CIDR blocks for intra (isolated) subnets | `map(string)` | n/a | yes |
| tgw_subnet_cidrs | CIDR blocks for Transit Gateway attachment subnets | `list(string)` | n/a | yes |
| map_public_ip_on_launch | Auto-assign public IP on launch | `bool` | n/a | yes |
| create_transit_gateway_attachment | Whether to create TGW VPC attachment | `bool` | n/a | yes |
| existing_transit_gateway_id | ID of existing Transit Gateway | `string` | n/a | yes |
| enable_dns_support_attachment | Enable DNS support for TGW attachment | `bool` | n/a | yes |
| enable_ipv6_support_attachment | Enable IPv6 support for TGW attachment | `bool` | n/a | yes |
| appliance_mode_support | Appliance mode for TGW attachment | `string` | "disable" | yes |
| tags | Tags to apply to resources | `map(string)` | n/a | yes |
| project_name | Project name for resource naming and tagging | `string` | n/a | yes |
| environment | Environment name for tagging | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | The ID of the VPC |
| private_eks_subnet_ids | IDs of the private EKS subnets |
| private_lb_subnet_ids | IDs of the private load balancer subnets |
| intra_subnet_ids | IDs of the intra (isolated) subnets |
| tgw_subnet_ids | IDs of the Transit Gateway attachment subnets |
| general_use_security_group_id | ID of the general use security group |
| eks_alb_sg_id | ID of the EKS ALB security group |
| bastion_sg_id | ID of the bastion security group |
| eks_worker_sg_id | ID of the EKS worker security group |

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Notes

- All subnets are private (no public IP assignment)
- Route tables are created for each subnet type
- Transit Gateway routes and VPC attachment are conditionally created based on `create_transit_gateway_attachment`
- Security groups are provided for general use, EKS, ALB, bastion, and worker nodes
- Subnets are tagged appropriately for EKS and load balancer integration 