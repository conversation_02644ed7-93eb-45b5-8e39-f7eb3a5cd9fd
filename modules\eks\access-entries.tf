# =============================================================================
# EKS ACCESS ENTRIES - USER ACCESS MANAGEMENT
# =============================================================================
# This file manages EKS access entries using the new EKS Access Entries API
# It maps IAM users/roles to Kubernetes RBAC groups
# This replaces the traditional aws-auth ConfigMap approach

# Note: The actual IAM users and roles are created by the Identity module
# This file only creates the EKS access entries that map those identities to Kubernetes groups

# -----------------------------------------------------------------------------
# DEVELOPER ACCESS ENTRY
# -----------------------------------------------------------------------------
# Maps the developer IAM user to Kubernetes groups for RBAC
# The developer user is created by the Identity module

resource "aws_eks_access_entry" "developer" {
  count         = var.create_developer_user ? 1 : 0
  cluster_name  = aws_eks_cluster.main.name
  principal_arn = var.developer_user_arn

  # Kubernetes groups that this user will be a member of
  # These groups should have corresponding ClusterRoles/Roles bound to them
  # Example: "my-viewer" group might be bound to a read-only ClusterRole
  kubernetes_groups = var.developer_kubernetes_groups

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer-access"
      Type = "developer-access-entry"
    }
  )
}

# -----------------------------------------------------------------------------
# MANAGER ACCESS ENTRY
# -----------------------------------------------------------------------------
# Maps the manager admin role to Kubernetes groups for RBAC
# The admin role is created by the Identity module

resource "aws_eks_access_entry" "manager" {
  count         = var.create_manager_role ? 1 : 0
  cluster_name  = aws_eks_cluster.main.name
  principal_arn = var.eks_admin_role_arn # Note: Using role ARN, not user ARN

  # Kubernetes groups for admin access
  # Example: "my-admin" group might be bound to cluster-admin ClusterRole
  kubernetes_groups = var.manager_kubernetes_groups

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-manager-access"
      Type = "manager-access-entry"
    }
  )
}
