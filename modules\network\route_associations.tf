# Route Table Associations
resource "aws_route_table_association" "private_eks_az1" {
  subnet_id      = aws_subnet.private_eks_az1.id
  route_table_id = aws_route_table.private_eks_az1.id
}
resource "aws_route_table_association" "private_eks_az2" {
  subnet_id      = aws_subnet.private_eks_az2.id
  route_table_id = aws_route_table.private_eks_az2.id
}
resource "aws_route_table_association" "private_lb_az1" {
  subnet_id      = aws_subnet.private_lb_az1.id
  route_table_id = aws_route_table.private_lb_az1.id
}
resource "aws_route_table_association" "private_lb_az2" {
  subnet_id      = aws_subnet.private_lb_az2.id
  route_table_id = aws_route_table.private_lb_az2.id
}
resource "aws_route_table_association" "intra_az1" {
  subnet_id      = aws_subnet.intra_az1.id
  route_table_id = aws_route_table.intra_az1.id
}
resource "aws_route_table_association" "intra_az2" {
  subnet_id      = aws_subnet.intra_az2.id
  route_table_id = aws_route_table.intra_az2.id
}
resource "aws_route_table_association" "tgw_attachment_1a" {
  subnet_id      = aws_subnet.tgw_attachment_1a.id
  route_table_id = aws_route_table.tgw_attachment_1a.id
}
resource "aws_route_table_association" "tgw_attachment_1b" {
  subnet_id      = aws_subnet.tgw_attachment_1b.id
  route_table_id = aws_route_table.tgw_attachment_1b.id
} 