# Helm Module

This module provides a comprehensive, production-ready deployment of essential Kubernetes applications using Helm charts. It's designed to work seamlessly with the EKS module and follows Kubernetes best practices for security, resource management, and operational excellence.

## 🏗️ Architecture Overview

The Helm module deploys critical Kubernetes infrastructure components:
- **Metrics Collection**: Resource usage metrics for autoscaling and monitoring
- **Auto Scaling**: Automatic cluster scaling based on workload demands
- **Load Balancing**: AWS-native load balancer integration
- **Security**: Certificate management and secrets handling
- **Storage**: Persistent volume support for stateful applications

## 📁 File Structure

- **providers.tf**: Helm and Kubernetes provider configuration with cluster authentication
- **metrics-server.tf**: Metrics Server for resource metrics and HPA/VPA support
- **cluster-autoscaler.tf**: Cluster Autoscaler for automatic node scaling
- **aws-load-balancer-controller.tf**: AWS Load Balancer Controller for ALB/NLB management
- **variables.tf**: Input variables and configuration options
- **outputs.tf**: Output values for monitoring deployment status

## 🔗 Integration with EKS Module

This module is designed to be used **after** the EKS cluster is deployed and requires specific connection information from the EKS module. The separation ensures:

- **Clean Architecture**: Infrastructure (EKS) vs Applications (Helm)
- **Independent Lifecycle**: Update applications without affecting cluster
- **Provider Isolation**: Each module manages its own provider configuration
- **Easier Troubleshooting**: Clear boundaries between components

## 🚀 Supported Applications

### 📊 Core Monitoring & Scaling Components

#### Metrics Server *(Enabled by default)*
- **Purpose**: Collects resource usage metrics (CPU, memory) from nodes and pods
- **Dependencies**: None
- **Required for**: Horizontal Pod Autoscaler (HPA), Vertical Pod Autoscaler (VPA), `kubectl top`
- **Deployment**: `kube-system` namespace with resource limits and node affinity
- **Configuration**: 15-second metric resolution, secure HTTPS endpoint

#### Cluster Autoscaler *(Recommended)*
- **Purpose**: Automatically scales EKS node groups based on pod resource demands
- **Dependencies**: Requires IAM role with Auto Scaling Group permissions
- **Features**: Least-waste scaling strategy, automatic node group discovery via tags
- **Deployment**: `kube-system` namespace with dedicated service account
- **Configuration**: Configurable scale-down delay and resource thresholds

### 🌐 Networking & Load Balancing

#### AWS Load Balancer Controller *(Recommended)*
- **Purpose**: Manages AWS Application Load Balancers (ALB) and Network Load Balancers (NLB)
- **Dependencies**: Requires IAM role with ALB/NLB management permissions
- **Features**:
  - Automatic ALB provisioning for Kubernetes Ingress resources
  - NLB support for LoadBalancer services
  - Integration with AWS WAF, ACM certificates
  - Target group binding for advanced routing
- **Deployment**: `kube-system` namespace with webhook configuration
- **Configuration**: VPC-aware, supports both public and private load balancers

#### NGINX Ingress Controller *(Optional)*
- **Purpose**: Alternative ingress controller for advanced routing and SSL termination
- **Use Case**: When you need features not available in ALB or want vendor-neutral ingress
- **Features**: Advanced routing, rate limiting, authentication, custom error pages
- **Deployment**: Dedicated namespace with configurable resource limits

### 🔐 Security & Secrets Management

#### Cert Manager *(Optional)*
- **Purpose**: Automatic TLS certificate provisioning and renewal
- **Features**:
  - Let's Encrypt integration for free SSL certificates
  - AWS Certificate Manager integration
  - Automatic certificate renewal
  - Support for multiple certificate authorities
- **Dependencies**: DNS validation requires Route53 or external DNS provider
- **Deployment**: Dedicated namespace with webhook and cainjector components

#### Secrets Store CSI Driver *(Optional)*
- **Purpose**: Mount secrets from AWS Secrets Manager and Parameter Store as volumes
- **Features**:
  - Secure secret injection without storing in etcd
  - Automatic secret rotation
  - Integration with Pod Identity or IRSA
- **Dependencies**: Requires IAM permissions for Secrets Manager/Parameter Store
- **Use Case**: Applications that need database passwords, API keys, certificates

### 💾 Storage Solutions

#### EFS CSI Driver *(Optional)*
- **Purpose**: Provides shared, persistent storage using Amazon EFS
- **Features**:
  - Multi-AZ shared storage for stateful applications
  - POSIX-compliant file system
  - Automatic scaling and high availability
  - Support for encryption at rest and in transit
- **Dependencies**: Requires IAM role with EFS permissions and existing EFS file system
- **Use Case**: Applications requiring shared storage (CMS, shared data processing)

## 🔐 IAM Requirements

### Required IAM Roles

Some Helm charts require IAM roles with specific AWS permissions. These roles should be created separately (either manually or via additional Terraform modules) and passed to this module.

#### 1. Cluster Autoscaler IAM Role
**Required when**: `enable_cluster_autoscaler = true`

**Permissions needed**:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "autoscaling:DescribeAutoScalingGroups",
        "autoscaling:DescribeAutoScalingInstances",
        "autoscaling:DescribeLaunchConfigurations",
        "autoscaling:DescribeTags",
        "autoscaling:SetDesiredCapacity",
        "autoscaling:TerminateInstanceInAutoScalingGroup",
        "ec2:DescribeLaunchTemplateVersions"
      ],
      "Resource": "*"
    }
  ]
}
```

**Trust Policy** (for Pod Identity):
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "pods.eks.amazonaws.com"
      },
      "Action": [
        "sts:AssumeRole",
        "sts:TagSession"
      ]
    }
  ]
}
```

#### 2. AWS Load Balancer Controller IAM Role
**Required when**: `enable_aws_load_balancer_controller = true`

**Permissions**: Use the AWS managed policy `AWSLoadBalancerControllerIAMPolicy` or create a custom policy with ALB/NLB management permissions.

**AWS CLI to get the policy**:
```bash
curl -o iam_policy.json https://raw.githubusercontent.com/kubernetes-sigs/aws-load-balancer-controller/v2.7.2/docs/install/iam_policy.json
aws iam create-policy --policy-name AWSLoadBalancerControllerIAMPolicy --policy-document file://iam_policy.json
```

#### 3. EFS CSI Driver IAM Role
**Required when**: `enable_efs_csi_driver = true`

**Permissions needed**:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "elasticfilesystem:DescribeAccessPoints",
        "elasticfilesystem:DescribeFileSystems",
        "elasticfilesystem:DescribeMountTargets",
        "ec2:DescribeAvailabilityZones"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "elasticfilesystem:CreateAccessPoint"
      ],
      "Resource": "*",
      "Condition": {
        "StringLike": {
          "aws:RequestedRegion": "us-west-2"
        }
      }
    }
  ]
}
```

### Manual Setup Requirements

#### 1. Create IAM Roles (One-time setup)
```bash
# Example: Create Cluster Autoscaler role
aws iam create-role --role-name eks-cluster-autoscaler-role --assume-role-policy-document file://trust-policy.json
aws iam attach-role-policy --role-name eks-cluster-autoscaler-role --policy-arn arn:aws:iam::ACCOUNT:policy/ClusterAutoscalerPolicy
```

#### 2. Tag Auto Scaling Groups (Required for Cluster Autoscaler)
Your EKS node group Auto Scaling Groups must have these tags:
```
k8s.io/cluster-autoscaler/enabled = true
k8s.io/cluster-autoscaler/YOUR_CLUSTER_NAME = owned
```

*Note: The EKS module should automatically add these tags to node groups.*

## 📋 Usage

### Basic Usage Example

```hcl
# First, deploy the EKS cluster
module "eks" {
  source = "./modules/eks"

  cluster_name    = "my-production-cluster"
  cluster_version = "1.28"
  project_name    = "my-project"
  environment     = "production"

  # ... other EKS configuration
}

# Authentication data source for Helm module
data "aws_eks_cluster_auth" "main" {
  name = module.eks.cluster_name
}

# Deploy Helm applications
module "helm" {
  source = "./modules/helm"

  # Cluster connection information (from EKS module)
  cluster_name           = module.eks.cluster_name
  cluster_endpoint       = module.eks.cluster_endpoint
  cluster_ca_certificate = module.eks.cluster_certificate_authority_data
  cluster_token          = data.aws_eks_cluster_auth.main.token

  # AWS and project configuration
  vpc_id       = module.vpc.vpc_id
  aws_region   = var.aws_region
  project_name = var.project_name
  environment  = var.environment
  common_tags  = var.common_tags

  # Core components (recommended for all clusters)
  enable_metrics_server     = true   # Required for HPA/VPA
  enable_cluster_autoscaler = true   # Automatic node scaling

  # Load balancing (choose one)
  enable_aws_load_balancer_controller = true   # AWS-native ALB/NLB
  enable_nginx_ingress                = false  # Alternative ingress

  # Security and secrets (optional)
  enable_cert_manager             = true   # Automatic SSL certificates
  enable_secrets_store_csi_driver = false # AWS Secrets Manager integration

  # Storage (optional)
  enable_efs_csi_driver = false  # Shared persistent storage

  # IAM Role ARNs (create these separately)
  cluster_autoscaler_role_arn           = aws_iam_role.cluster_autoscaler.arn
  aws_load_balancer_controller_role_arn = aws_iam_role.aws_load_balancer_controller.arn
  efs_csi_driver_role_arn              = aws_iam_role.efs_csi_driver.arn

  # Ensure EKS cluster is ready before deploying applications
  depends_on = [module.eks]
}
```

### Production Configuration Example

```hcl
module "helm" {
  source = "./modules/helm"

  # ... connection configuration ...

  # Production-ready configuration
  enable_metrics_server              = true
  enable_cluster_autoscaler          = true
  enable_aws_load_balancer_controller = true
  enable_cert_manager                = true
  enable_secrets_store_csi_driver    = true
  enable_efs_csi_driver              = true

  # Custom chart versions for stability
  metrics_server_version              = "3.11.0"
  cluster_autoscaler_version          = "9.29.0"
  aws_load_balancer_controller_version = "1.6.2"
  cert_manager_version                = "1.13.1"

  # Production tags
  common_tags = {
    Environment = "production"
    Project     = "my-project"
    Owner       = "platform-team"
    CostCenter  = "engineering"
  }
}
```

## 🔧 Operational Guidance

### Deployment Order

1. **Deploy EKS cluster** using the EKS module
2. **Verify cluster is ready**: `kubectl get nodes`
3. **Create IAM roles** for Helm components (if not using Pod Identity)
4. **Deploy Helm module** with required components
5. **Verify deployments**: Check pod status and logs

### Authentication Token

The module requires an authentication token to connect to the EKS cluster:

```hcl
data "aws_eks_cluster_auth" "main" {
  name = module.eks.cluster_name
}
```

**Important**: This token is temporary and automatically refreshed by the AWS provider. Do not hardcode tokens.

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Helm Release Fails to Deploy
**Symptoms**: Terraform shows helm_release resource failed
**Causes**:
- Cluster not ready
- Insufficient IAM permissions
- Network connectivity issues

**Solutions**:
```bash
# Check cluster status
kubectl get nodes

# Check Helm release status
helm list -A

# Check pod logs
kubectl logs -n kube-system deployment/metrics-server
```

#### 2. Cluster Autoscaler Not Scaling
**Symptoms**: Pods remain pending, nodes not added
**Causes**:
- Missing IAM permissions
- Auto Scaling Group tags missing
- Resource requests not specified on pods

**Solutions**:
```bash
# Check Cluster Autoscaler logs
kubectl logs -n kube-system deployment/cluster-autoscaler

# Verify ASG tags
aws autoscaling describe-auto-scaling-groups --query 'AutoScalingGroups[*].{Name:AutoScalingGroupName,Tags:Tags}'

# Check pod resource requests
kubectl describe pod <pending-pod-name>
```

#### 3. Load Balancer Controller Issues
**Symptoms**: Ingress resources not creating ALBs
**Causes**:
- Missing IAM permissions
- Subnet tags missing
- Security group issues

**Solutions**:
```bash
# Check controller logs
kubectl logs -n kube-system deployment/aws-load-balancer-controller

# Verify subnet tags (required for ALB)
aws ec2 describe-subnets --query 'Subnets[*].{SubnetId:SubnetId,Tags:Tags}'

# Check Ingress events
kubectl describe ingress <ingress-name>
```

#### 4. Metrics Server Not Working
**Symptoms**: `kubectl top` commands fail, HPA not working
**Causes**:
- TLS certificate issues
- Kubelet connectivity problems
- Resource constraints

**Solutions**:
```bash
# Check metrics-server logs
kubectl logs -n kube-system deployment/metrics-server

# Test metrics API
kubectl get --raw "/apis/metrics.k8s.io/v1beta1/nodes"

# Check kubelet status
kubectl get nodes -o wide
```

### Debugging Commands

```bash
# Check all Helm releases
helm list -A

# Get detailed release information
helm get all <release-name> -n <namespace>

# Check pod status across all namespaces
kubectl get pods -A

# Check events for issues
kubectl get events --sort-by=.metadata.creationTimestamp

# Check resource usage
kubectl top nodes
kubectl top pods -A
```

### Recovery Procedures

#### Reinstall Failed Helm Release
```bash
# Delete failed release
helm uninstall <release-name> -n <namespace>

# Clean up remaining resources
kubectl delete all -l app.kubernetes.io/instance=<release-name> -n <namespace>

# Redeploy via Terraform
terraform apply -target=module.helm.helm_release.<resource-name>
```

#### Reset Cluster Autoscaler
```bash
# Delete the deployment
kubectl delete deployment cluster-autoscaler -n kube-system

# Terraform will recreate it
terraform apply -target=module.helm.helm_release.cluster_autoscaler
```

## 📊 Component Details

### Metrics Server
- **Namespace**: `kube-system`
- **Purpose**: Provides CPU and memory metrics for HPA, VPA, and `kubectl top`
- **Configuration**:
  - Resource limits: 200m CPU, 400Mi memory
  - 15-second metric collection interval
  - Node affinity for master nodes
  - Secure HTTPS endpoint on port 4443
- **Dependencies**: None
- **Health Check**: `kubectl top nodes`

### Cluster Autoscaler
- **Namespace**: `kube-system`
- **Purpose**: Automatically scales EKS node groups based on pod demands
- **Configuration**:
  - Least-waste expander strategy
  - Automatic node group discovery via tags
  - Configurable scale-down delay
  - Resource limits: 100m CPU, 300Mi memory
- **Dependencies**: IAM role with Auto Scaling permissions
- **Health Check**: `kubectl logs -n kube-system deployment/cluster-autoscaler`

### AWS Load Balancer Controller
- **Namespace**: `kube-system`
- **Purpose**: Manages AWS ALB and NLB for Kubernetes Ingress and LoadBalancer services
- **Configuration**:
  - VPC-aware load balancer provisioning
  - Support for public and private load balancers
  - Integration with AWS WAF and ACM
  - Webhook configuration for admission control
- **Dependencies**: IAM role with ALB/NLB permissions, properly tagged subnets
- **Health Check**: `kubectl get ingress` and check AWS console for ALBs

## 📋 Requirements

| Name | Version | Purpose |
|------|---------|---------|
| terraform | >= 1.0 | Infrastructure as Code |
| helm | >= 2.12 | Kubernetes package manager |
| kubernetes | >= 2.24 | Kubernetes API compatibility |
| aws | >= 5.0 | AWS provider for authentication |

## 🔌 Providers

| Name | Version | Configuration |
|------|---------|---------------|
| helm | >= 2.12 | Configured with EKS cluster authentication |
| kubernetes | >= 2.24 | Configured with EKS cluster authentication |

## 📥 Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| cluster_name | EKS cluster name | string | n/a | yes |
| cluster_endpoint | EKS cluster API endpoint | string | n/a | yes |
| cluster_ca_certificate | EKS cluster CA certificate (base64) | string | n/a | yes |
| cluster_token | EKS cluster authentication token | string | n/a | yes |
| vpc_id | VPC ID where cluster is deployed | string | n/a | yes |
| aws_region | AWS region | string | n/a | yes |
| enable_metrics_server | Deploy Metrics Server | bool | true | no |
| enable_cluster_autoscaler | Deploy Cluster Autoscaler | bool | false | no |
| enable_aws_load_balancer_controller | Deploy AWS Load Balancer Controller | bool | false | no |

*See `variables.tf` for complete list of input variables.*

## 📤 Outputs

| Name | Description |
|------|-------------|
| metrics_server_status | Metrics Server deployment status |
| cluster_autoscaler_status | Cluster Autoscaler deployment status |
| aws_load_balancer_controller_status | AWS Load Balancer Controller deployment status |

*See `outputs.tf` for complete list of outputs.*

## 📝 Best Practices

### Resource Management
- All charts include resource requests and limits
- Node selectors ensure Linux-only scheduling
- Tolerations allow system component scheduling
- Anti-affinity rules prevent single points of failure

### Security
- Service accounts use least-privilege IAM roles
- Pod Security Standards compliance
- Network policies for traffic isolation
- Regular security updates via version pinning

### Monitoring
- Comprehensive logging to CloudWatch
- Metrics collection for observability
- Health checks and readiness probes
- Alert integration with monitoring systems

### Operational Excellence
- Version pinning for consistent deployments
- Graceful handling of node maintenance
- Automatic rollback on deployment failures
- Documentation and runbooks for troubleshooting
