# Terraform Backend Bootstrap Project

This project creates the S3 buckets and DynamoDB tables required for Terraform remote state management for **both development and production environments**. It is designed to be run **once** to bootstrap the backend infrastructure for other Terraform projects.

> **Note:** After running this bootstrap project, you will use the generated backend configuration in your main Terraform project, which is fully modular and uses a single `network` module for all VPC, subnet, TGW, and security group resources. See the [main project README](../README.md) for details on the modular structure and next steps.

## ⚠️ Critical Architecture Decision

This bootstrap project uses **local state** to avoid the circular dependency problem. The S3 buckets and DynamoDB tables it creates will be used by other Terraform projects, but this project itself stores its state locally.

## Why This Approach?

- **Safety**: Eliminates the risk of `terraform destroy` deleting the backend that contains the state
- **Separation of Concerns**: Backend infrastructure is managed separately from application infrastructure
- **Best Practice**: Follows HashiCorp's recommended pattern for production Terraform workflows
- **Multi-Environment**: Supports both dev and prod backends for safe, isolated state management

## Prerequisites

- AWS CLI configured with appropriate permissions
- Terraform >= 1.0
- Permissions to create S3 buckets and DynamoDB tables

## Usage

### 1. Configure Variables

Edit `terraform.tfvars` to set your desired configuration:

```hcl
aws_region = "eu-west-1"
# Dev Backend
dev_backend_bucket_name = "eks-main-terraform-state-ireland-dev"
dev_dynamodb_table_name = "terraform-state-lock-dev"
# Prod Backend
prod_backend_bucket_name = "eks-main-terraform-state-ireland-prod"
prod_dynamodb_table_name = "terraform-state-lock-prod"
billing_mode = "PAY_PER_REQUEST"
```

### 2. Initialize and Apply

```bash
cd bootstrap
terraform init
terraform plan
terraform apply
```

### 3. Verify Outputs

After successful deployment, note the outputs:

```bash
terraform output dev_backend_config_hcl
terraform output prod_backend_config_hcl
```

These outputs provide the backend configuration needed for your main Terraform project's `config/dev.backend.hcl` and `config/prod.backend.hcl` files.

## Outputs

- `dev_backend_config_hcl`: Backend configuration in HCL format for dev environment
- `prod_backend_config_hcl`: Backend configuration in HCL format for prod environment

## Example Backend Config Files

**config/dev.backend.hcl**
```hcl
# Copy from terraform output dev_backend_config_hcl
bucket         = "eks-main-terraform-state-ireland-dev"
key            = "terraform-dev.tfstate"
dynamodb_table = "terraform-state-lock-dev"
region         = "eu-west-1"
encrypt        = true
use_lockfile   = true
```

**config/prod.backend.hcl**
```hcl
# Copy from terraform output prod_backend_config_hcl
bucket         = "eks-main-terraform-state-ireland-prod"
key            = "terraform-prod.tfstate"
dynamodb_table = "terraform-state-lock-prod"
region         = "eu-west-1"
encrypt        = true
use_lockfile   = true
```

## Security Features

- **S3 Buckets**: Versioning enabled, encryption at rest, public access blocked
- **DynamoDB Tables**: Encryption at rest, point-in-time recovery enabled
- **Lifecycle Protection**: S3 buckets have `prevent_destroy = true`

## State Management

This project's state file (`terraform.tfstate`) is stored locally in the `bootstrap/` directory. This file should be:

- **Backed up** regularly
- **Version controlled** (if desired)
- **Protected** from accidental deletion

## Cleanup

To remove the backend infrastructure:

1. **WARNING**: This will affect ALL Terraform projects using this backend
2. Ensure no other projects are actively using the backend
3. Run: `terraform destroy`

## Integration with Main Project

After running this bootstrap project, use the outputted backend config for your main Terraform project's backend config files (`config/dev.backend.hcl` and `config/prod.backend.hcl`).

Example usage in main project:

```bash
terraform init -backend-config=config/dev.backend.hcl
terraform plan -var-file=dev.tfvars
terraform apply -var-file=dev.tfvars

# For production:
terraform init -backend-config=config/prod.backend.hcl
terraform plan -var-file=production.tfvars
terraform apply -var-file=production.tfvars 