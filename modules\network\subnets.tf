# Private EKS Subnets
resource "aws_subnet" "private_eks_az1" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_eks_subnet_cidrs.az1
  availability_zone       = var.availability_zones[0]
  map_public_ip_on_launch = false
  tags = merge(
    var.common_tags,
    {
      Name                              = "${var.project_name}-${var.environment}-private-eks-az1"
      Environment                       = var.environment
      Project                           = var.project_name
      Type                              = "Private EKS"
      AZ                                = var.availability_zones[0]
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}
resource "aws_subnet" "private_eks_az2" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_eks_subnet_cidrs.az2
  availability_zone       = var.availability_zones[1]
  map_public_ip_on_launch = false
  tags = merge(
    var.common_tags,
    {
      Name                              = "${var.project_name}-${var.environment}-private-eks-az2"
      Environment                       = var.environment
      Project                           = var.project_name
      Type                              = "Private EKS"
      AZ                                = var.availability_zones[1]
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}
# Private Load Balancer Subnets
resource "aws_subnet" "private_lb_az1" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_lb_subnet_cidrs.az1
  availability_zone       = var.availability_zones[0]
  map_public_ip_on_launch = false
  tags = merge(
    var.common_tags,
    {
      Name                              = "${var.project_name}-${var.environment}-private-lb-az1"
      Environment                       = var.environment
      Project                           = var.project_name
      Type                              = "Private Load Balancer"
      AZ                                = var.availability_zones[0]
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}
resource "aws_subnet" "private_lb_az2" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_lb_subnet_cidrs.az2
  availability_zone       = var.availability_zones[1]
  map_public_ip_on_launch = false
  tags = merge(
    var.common_tags,
    {
      Name                              = "${var.project_name}-${var.environment}-private-lb-az2"
      Environment                       = var.environment
      Project                           = var.project_name
      Type                              = "Private Load Balancer"
      AZ                                = var.availability_zones[1]
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}
# Intra (Isolated) Subnets
resource "aws_subnet" "intra_az1" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.intra_subnet_cidrs.az1
  availability_zone       = var.availability_zones[0]
  map_public_ip_on_launch = false
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-intra-az1"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated)"
      AZ          = var.availability_zones[0]
    }
  )
}
resource "aws_subnet" "intra_az2" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.intra_subnet_cidrs.az2
  availability_zone       = var.availability_zones[1]
  map_public_ip_on_launch = false
  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-${var.environment}-intra-az2"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated)"
      AZ          = var.availability_zones[1]
    }
  )
} 