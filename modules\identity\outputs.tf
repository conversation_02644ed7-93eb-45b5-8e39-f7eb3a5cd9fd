# =============================================================================
# IDENTITY MODULE OUTPUTS
# =============================================================================
# This file defines all output values from the identity module

# -----------------------------------------------------------------------------
# EKS CLUSTER ROLE OUTPUTS
# -----------------------------------------------------------------------------

output "cluster_service_role_arn" {
  description = "ARN of the EKS cluster service role"
  value       = aws_iam_role.cluster.arn
}

output "cluster_service_role_name" {
  description = "Name of the EKS cluster service role"
  value       = aws_iam_role.cluster.name
}

# -----------------------------------------------------------------------------
# EKS NODE GROUP ROLE OUTPUTS
# -----------------------------------------------------------------------------

output "node_group_role_arn" {
  description = "ARN of the EKS node group role"
  value       = aws_iam_role.node_group.arn
}

output "node_group_role_name" {
  description = "Name of the EKS node group role"
  value       = aws_iam_role.node_group.name
}

# -----------------------------------------------------------------------------
# CSI DRIVER ROLE OUTPUTS
# -----------------------------------------------------------------------------

output "ebs_csi_driver_role_arn" {
  description = "ARN of the EBS CSI Driver IAM role"
  value       = var.enable_ebs_csi_driver ? aws_iam_role.ebs_csi_driver[0].arn : null
}

output "ebs_csi_driver_role_name" {
  description = "Name of the EBS CSI Driver IAM role"
  value       = var.enable_ebs_csi_driver ? aws_iam_role.ebs_csi_driver[0].name : null
}

output "efs_csi_driver_role_arn" {
  description = "ARN of the EFS CSI Driver IAM role"
  value       = var.enable_efs ? aws_iam_role.efs_csi_driver[0].arn : null
}

output "efs_csi_driver_role_name" {
  description = "Name of the EFS CSI Driver IAM role"
  value       = var.enable_efs ? aws_iam_role.efs_csi_driver[0].name : null
}

# -----------------------------------------------------------------------------
# DEVELOPER ACCESS OUTPUTS
# -----------------------------------------------------------------------------

output "developer_user_arn" {
  description = "ARN of the developer IAM user"
  value       = var.create_developer_user ? aws_iam_user.developer[0].arn : null
}

output "developer_user_name" {
  description = "Name of the developer IAM user"
  value       = var.create_developer_user ? aws_iam_user.developer[0].name : null
}

output "developer_access_key_id" {
  description = "Access key ID for the developer user"
  value       = var.create_developer_user ? aws_iam_access_key.developer[0].id : null
  sensitive   = true
}

output "developer_secret_access_key" {
  description = "Secret access key for the developer user"
  value       = var.create_developer_user ? aws_iam_access_key.developer[0].secret : null
  sensitive   = true
}

# -----------------------------------------------------------------------------
# MANAGER ACCESS OUTPUTS
# -----------------------------------------------------------------------------

output "manager_user_arn" {
  description = "ARN of the manager IAM user"
  value       = var.create_manager_role ? aws_iam_user.manager[0].arn : null
}

output "manager_user_name" {
  description = "Name of the manager IAM user"
  value       = var.create_manager_role ? aws_iam_user.manager[0].name : null
}

output "manager_access_key_id" {
  description = "Access key ID for the manager user"
  value       = var.create_manager_role ? aws_iam_access_key.manager[0].id : null
  sensitive   = true
}

output "manager_secret_access_key" {
  description = "Secret access key for the manager user"
  value       = var.create_manager_role ? aws_iam_access_key.manager[0].secret : null
  sensitive   = true
}

output "eks_admin_role_arn" {
  description = "ARN of the EKS admin IAM role"
  value       = var.create_manager_role ? aws_iam_role.eks_admin[0].arn : null
}

output "eks_admin_role_name" {
  description = "Name of the EKS admin IAM role"
  value       = var.create_manager_role ? aws_iam_role.eks_admin[0].name : null
}

# -----------------------------------------------------------------------------
# SUMMARY OUTPUTS
# -----------------------------------------------------------------------------

output "all_roles" {
  description = "Map of all IAM roles created by this module"
  value = {
    cluster_service_role = {
      arn  = aws_iam_role.cluster.arn
      name = aws_iam_role.cluster.name
    }
    node_group_role = {
      arn  = aws_iam_role.node_group.arn
      name = aws_iam_role.node_group.name
    }
    ebs_csi_driver_role = var.enable_ebs_csi_driver ? {
      arn  = aws_iam_role.ebs_csi_driver[0].arn
      name = aws_iam_role.ebs_csi_driver[0].name
    } : null
    efs_csi_driver_role = var.enable_efs ? {
      arn  = aws_iam_role.efs_csi_driver[0].arn
      name = aws_iam_role.efs_csi_driver[0].name
    } : null
    eks_admin_role = var.create_manager_role ? {
      arn  = aws_iam_role.eks_admin[0].arn
      name = aws_iam_role.eks_admin[0].name
    } : null
  }
}

output "all_users" {
  description = "Map of all IAM users created by this module"
  value = {
    developer_user = var.create_developer_user ? {
      arn  = aws_iam_user.developer[0].arn
      name = aws_iam_user.developer[0].name
    } : null
    manager_user = var.create_manager_role ? {
      arn  = aws_iam_user.manager[0].arn
      name = aws_iam_user.manager[0].name
    } : null
  }
}
